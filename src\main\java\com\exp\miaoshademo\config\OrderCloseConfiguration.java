package com.exp.miaoshademo.config;

import com.exp.miaoshademo.dto.OrderCloseTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 订单关闭模块配置类
 */
@Slf4j
@Configuration
@EnableScheduling
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "order.close", name = "enabled", havingValue = "true", matchIfMissing = true)
public class OrderCloseConfiguration {
    
    private final OrderCloseConfig orderCloseConfig;
    
    /**
     * 订单关闭任务阻塞队列
     */
    @Bean
    public BlockingQueue<OrderCloseTask> orderCloseQueue() {
        BlockingQueue<OrderCloseTask> queue = new LinkedBlockingQueue<>(orderCloseConfig.getQueueCapacity());
        log.info("创建订单关闭任务队列，容量: {}", orderCloseConfig.getQueueCapacity());
        return queue;
    }
}
