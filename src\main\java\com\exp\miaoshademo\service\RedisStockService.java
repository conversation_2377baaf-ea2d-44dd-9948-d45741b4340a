package com.exp.miaoshademo.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class RedisStockService {

    public final RedisTemplate<String, Object> redisTemplate;
    private final DefaultRedisScript<List> seckillStockScript;

    private static final String STOCK_KEY_PREFIX = "seckill:stock:";
    private static final String USER_KEY_PREFIX = "seckill:user:";
    private static final int USER_RECORD_EXPIRE_TIME = 3600; // 1小时过期

    public void initStock(Long productId, Integer stock) {
        String stockKey = STOCK_KEY_PREFIX + productId;
        redisTemplate.opsForValue().set(stockKey, stock);
        log.info("初始化商品{}库存为{}", productId, stock);
    }

    public Integer getStock(Long productId) {
        String stockKey = STOCK_KEY_PREFIX + productId;
        Object stock = redisTemplate.opsForValue().get(stockKey);
        return stock != null ? Integer.valueOf(stock.toString()) : 0;
    }

    // 秒杀库存扣减
    public SeckillResult seckillStock(Long userId, Long productId, Integer quantity) {
        String stockKey = STOCK_KEY_PREFIX + productId;
        String userKey = USER_KEY_PREFIX + userId + ":" + productId;
        
        List<String> keys = Arrays.asList(stockKey, userKey);
        List<String> args = Arrays.asList(
            userId.toString(),
            productId.toString(),
            quantity.toString(),
            String.valueOf(USER_RECORD_EXPIRE_TIME)
        );
        
        try {
            List result = redisTemplate.execute(seckillStockScript, keys, args.toArray());
            
            if (result != null && result.size() >= 2) {
                Integer code = Integer.valueOf(result.get(0).toString());
                String message = result.get(1).toString();
                Integer remainStock = result.size() > 2 ? Integer.valueOf(result.get(2).toString()) : 0;
                
                log.info("秒杀结果 - 用户:{}, 商品:{}, 数量:{}, 结果码:{}, 消息:{}, 剩余库存:{}", 
                    userId, productId, quantity, code, message, remainStock);
                
                return new SeckillResult(code, message, remainStock);
            }
            
            return new SeckillResult(-999, "执行Lua脚本失败", 0);
        } catch (Exception e) {
            log.error("执行秒杀库存扣减失败", e);
            return new SeckillResult(-999, "系统异常", 0);
        }
    }

    public void syncStockFromDB(Long productId, Integer stock) {
        String stockKey = STOCK_KEY_PREFIX + productId;
        redisTemplate.opsForValue().set(stockKey, stock, 1, TimeUnit.HOURS);
        log.info("同步商品{}库存到Redis: {}", productId, stock);
    }

    public void deleteUserRecord(Long userId, Long productId) {
        String userKey = USER_KEY_PREFIX + userId + ":" + productId;
        redisTemplate.delete(userKey);
        log.info("删除用户{}商品{}购买记录", userId, productId);
    }

    public static class SeckillResult {
        private Integer code;
        private String message;
        private Integer remainStock;

        public SeckillResult(Integer code, String message, Integer remainStock) {
            this.code = code;
            this.message = message;
            this.remainStock = remainStock;
        }

        public boolean isSuccess() {
            return code == 1;
        }

        public Integer getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }

        public Integer getRemainStock() {
            return remainStock;
        }
    }
}