package com.exp.miaoshademo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单关闭任务数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderCloseTask implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 购买数量
     */
    private Integer quantity;
    
    /**
     * 秒杀价格
     */
    private BigDecimal seckillPrice;
    
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 订单状态
     */
    private Integer status;
    
    /**
     * 订单创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 任务创建时间戳
     */
    private Long taskTimestamp;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 0;
    
    public OrderCloseTask(Long orderId, String orderNo, Long userId, Long productId, 
                         String productName, Integer quantity, BigDecimal seckillPrice, 
                         BigDecimal totalAmount, Integer status, LocalDateTime createdTime) {
        this.orderId = orderId;
        this.orderNo = orderNo;
        this.userId = userId;
        this.productId = productId;
        this.productName = productName;
        this.quantity = quantity;
        this.seckillPrice = seckillPrice;
        this.totalAmount = totalAmount;
        this.status = status;
        this.createdTime = createdTime;
        this.taskTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }
    
    /**
     * 判断是否超过最大重试次数
     */
    public boolean isMaxRetryExceeded(int maxRetry) {
        return this.retryCount >= maxRetry;
    }
}
