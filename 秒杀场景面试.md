# 秒杀场景面试题详解

## 目录
1. [秒杀系统核心问题](#1-秒杀系统核心问题)
2. [技术架构问题](#2-技术架构问题)
3. [分布式事务问题](#3-分布式事务问题)
4. [性能优化问题](#4-性能优化问题)
5. [容灾与监控问题](#5-容灾与监控问题)

---

## 1. 秒杀系统核心问题

### Q1: 秒杀系统的核心挑战是什么？你是如何解决的？

**回答示例：**

秒杀系统面临三大核心挑战：

1. **高并发冲击**：瞬间大量用户涌入，QPS可能达到数万甚至十万级别
2. **超卖问题**：库存扣减的原子性保证，防止卖出超过库存的商品
3. **系统稳定性**：在高负载下保证系统不崩溃，用户体验良好

**我的解决方案：**
- **多层架构**：CDN + 负载均衡 + 应用集群 + 缓存 + 数据库
- **Redis + Lua脚本**：保证库存扣减的原子性，防止超卖
- **RocketMQ事务消息**：确保Redis和MySQL数据最终一致性
- **限流机制**：接口级、IP级、用户级多维度限流
- **异步处理**：核心业务异步化，提升响应速度

---

### Q2: 如何防止秒杀中的超卖问题？

**回答示例：**

超卖问题是秒杀系统的核心技术难点，我采用了多层防护：

**第一层：Redis + Lua脚本**
```lua
-- 原子性检查库存并扣减
local currentStock = redis.call('GET', stockKey)
if tonumber(currentStock) >= quantity then
    redis.call('DECR', stockKey, quantity)
    return {1, "成功"}
else
    return {-1, "库存不足"}
end
```

**第二层：数据库乐观锁**
```sql
UPDATE seckill_product 
SET available_stock = available_stock - #{quantity}, version = version + 1
WHERE id = #{productId} AND available_stock >= #{quantity} AND version = #{version}
```

**第三层：业务层校验**
- 用户购买资格验证
- 重复购买检测
- 库存双重校验

这样确保在任何情况下都不会出现超卖。

---

### Q3: 秒杀系统的限流策略有哪些？

**回答示例：**

我设计了多维度的限流策略：

**1. 接口级限流**
```java
@RateLimit(count = 5, time = 10, message = "秒杀过于频繁，请稍后再试")
public Map<String, Object> doSeckill(...)
```

**2. 用户级限流**
- 同一用户10秒内最多5次请求
- 基于Redis实现滑动窗口算法

**3. IP级限流**
- 防止恶意IP刷单
- 结合地理位置进行风控

**4. 系统级熔断**
- 当系统负载过高时自动熔断
- 降级到排队页面或错误页面

**5. 网关层限流**
- 在Nginx或API网关层进行初步限流
- 减少到达应用层的无效请求

**实现原理：**
```lua
-- Redis滑动窗口限流
local current = redis.call('INCR', key)
if current == 1 then
    redis.call('EXPIRE', key, window)
end
return current <= limit
```

---

## 2. 技术架构问题

### Q4: 为什么选择Redis而不是其他缓存方案？

**回答示例：**

选择Redis主要基于以下考虑：

**1. 原子性操作支持**
- Redis的INCR/DECR操作是原子性的
- Lua脚本可以保证多个操作的原子性
- 这对库存扣减至关重要

**2. 数据结构丰富**
- String：存储库存数量
- Hash：存储商品详细信息
- Set：存储参与用户列表
- ZSet：实现排行榜功能

**3. 高性能**
- 单线程模型避免锁竞争
- 内存操作，QPS可达10万+
- 支持管道批量操作

**4. 持久化策略**
- RDB + AOF双重保障
- 即使宕机也能快速恢复

**5. 集群支持**
- Redis Cluster支持水平扩展
- 主从复制保证高可用

**对比其他方案：**
- Memcached：不支持复杂数据结构和持久化
- Hazelcast：Java生态，但性能不如Redis
- 本地缓存：无法保证集群数据一致性

---

### Q5: RocketMQ事务消息的执行流程是怎样的？

**回答示例：**

RocketMQ事务消息是我们保证分布式事务一致性的核心，流程如下：

**第一阶段：发送半消息**
```java
// 发送事务消息
TransactionSendResult result = rocketMQTemplate.sendMessageInTransaction(
    destination, message, seckillMessage);
```

**第二阶段：执行本地事务**
```java
@Override
public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
    try {
        // 执行Redis库存扣减
        RedisStockService.SeckillResult result = redisStockService.seckillStock(...);
        
        if (result.isSuccess()) {
            return RocketMQLocalTransactionState.COMMIT;  // 提交消息
        } else {
            return RocketMQLocalTransactionState.ROLLBACK; // 回滚消息
        }
    } catch (Exception e) {
        return RocketMQLocalTransactionState.ROLLBACK;
    }
}
```

**第三阶段：消息回查**
```java
@Override
public RocketMQLocalTransactionState checkLocalTransaction(Message msg) {
    // 检查Redis中的用户购买记录是否存在
    // 存在说明本地事务已成功，返回COMMIT
    // 不存在说明本地事务失败，返回ROLLBACK
}
```

**消费端处理**
```java
// 只有COMMIT的消息才会被消费者接收
@RocketMQMessageListener(topic = "SECKILL_TOPIC")
public void onMessage(String message) {
    // 执行MySQL数据库操作：创建订单、扣减DB库存、记录流水
}
```

**关键优势：**
1. 最终一致性保证
2. 避免分布式锁的性能开销
3. 自动重试和回查机制
4. 支持高并发场景

---

### Q6: Lua脚本在秒杀中的作用是什么？能否用其他方式替代？

**回答示例：**

Lua脚本在秒杀系统中起到关键作用：

**核心作用：**
1. **原子性保证**：多个Redis操作在一个脚本中执行，要么全成功要么全失败
2. **减少网络往返**：多个操作一次性发送到Redis服务器
3. **避免竞态条件**：脚本执行期间其他客户端无法干扰

**我们的Lua脚本：**
```lua
-- 检查用户是否已购买
local userBought = redis.call('EXISTS', userKey)
if userBought == 1 then
    return {-1, "用户已购买过该商品"}
end

-- 检查并扣减库存
local currentStock = redis.call('GET', stockKey)
if tonumber(currentStock) < quantity then
    return {-3, "库存不足"}
end

-- 原子性执行：扣减库存 + 记录用户
redis.call('DECRBY', stockKey, quantity)
redis.call('SETEX', userKey, expireTime, userId)
```

**其他替代方案的问题：**

1. **分布式锁**
   - 性能开销大，吞吐量低
   - 锁竞争激烈，响应时间不稳定
   - 需要处理锁超时、死锁等问题

2. **数据库乐观锁**
   - 并发能力受限于数据库性能
   - 大量失败重试，用户体验差
   - 无法利用Redis的高性能优势

3. **队列串行化**
   - 吞吐量受限于单队列处理能力
   - 实时性差，用户等待时间长
   - 队列积压时容易造成雪崩

**结论：** Lua脚本是在Redis环境下实现原子性操作的最优方案。

---

## 3. 分布式事务问题

### Q7: 分布式事务的CAP理论在秒杀系统中如何体现？

**回答示例：**

CAP理论在秒杀系统中的体现和权衡：

**C (一致性)**
- **强一致性要求**：库存数据不能出现不一致
- **我们的保证**：通过Redis Lua脚本 + RocketMQ事务消息
- **权衡**：牺牲部分实时性，采用最终一致性

**A (可用性)**
- **高可用要求**：系统不能因为部分组件故障而整体不可用
- **我们的保证**：
  - Redis主从切换
  - RocketMQ集群部署
  - 数据库读写分离
  - 多级限流和熔断

**P (分区容错性)**
- **网络分区处理**：当网络出现问题时系统仍能运行
- **我们的策略**：
  - 本地缓存兜底
  - 降级机制
  - 异步补偿

**在秒杀场景中的选择：CP模式**
```
Redis库存扣减 (C) + 最终一致性 (A) + 网络分区处理 (P)
```

**具体实现：**
1. **Redis扣减保证C**：原子性操作确保库存一致性
2. **异步消息保证A**：系统不会因为数据库慢而阻塞
3. **降级机制保证P**：网络分区时仍能提供基本服务

**权衡策略：**
- 优先保证库存不超卖（C）
- 通过异步处理保证高可用（A）
- 设计降级方案应对分区（P）

---

### Q8: 如何保证Redis和MySQL的数据最终一致性？

**回答示例：**

保证Redis和MySQL数据最终一致性是分布式系统的核心问题，我采用了多重保障机制：

**1. RocketMQ事务消息机制**
```java
// 第一阶段：Redis扣减作为本地事务
public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
    // Redis扣减成功 -> COMMIT消息
    // Redis扣减失败 -> ROLLBACK消息
}

// 第二阶段：消费者处理MySQL操作
@RocketMQMessageListener(topic = "SECKILL_TOPIC")
public void onMessage(String message) {
    // 执行MySQL：创建订单、扣减库存、记录流水
}
```

**2. 消息重试机制**
```java
// 消费失败自动重试
@RocketMQMessageListener(
    topic = "SECKILL_TOPIC",
    consumeMode = ConsumeMode.ORDERLY, // 保证顺序
    maxReconsumeTimes = 3 // 最大重试次数
)
```

**3. 补偿机制**
```java
// 定时任务检查数据一致性
@Scheduled(fixedRate = 60000) // 每分钟检查一次
public void checkDataConsistency() {
    // 比较Redis和MySQL的库存差异
    // 发现不一致时进行补偿
}
```

**4. 幂等性保证**
```java
// 通过唯一订单号保证幂等性
@Transactional
public void createOrder(SeckillMessage message) {
    // 先检查订单是否已存在
    if (orderExists(message.getOrderNo())) {
        return; // 幂等处理
    }
    // 执行订单创建逻辑
}
```

**5. 死信队列处理**
```java
// 处理多次重试仍失败的消息
@RocketMQMessageListener(topic = "SECKILL_DLQ")
public void handleDeadLetter(String message) {
    // 记录到异常表，人工处理
    // 或者触发告警
}
```

**数据一致性检查工具：**
```java
public class DataConsistencyChecker {
    public void checkStockConsistency(Long productId) {
        Integer redisStock = redisStockService.getStock(productId);
        Integer dbStock = databaseStockService.getStock(productId);
        
        if (!Objects.equals(redisStock, dbStock)) {
            // 触发数据修复流程
            repairData(productId, redisStock, dbStock);
        }
    }
}
```

---

## 4. 性能优化问题

### Q9: 秒杀系统的性能瓶颈在哪里？如何优化？

**回答示例：**

秒杀系统的性能瓶颈分析和优化策略：

**1. 网络I/O瓶颈**
- **问题**：大量并发请求导致网络带宽饱和
- **优化**：
  ```java
  // CDN静态资源缓存
  // 网关层请求合并
  // 启用HTTP/2多路复用
  ```

**2. 数据库瓶颈**
- **问题**：MySQL并发写入性能有限
- **优化**：
  ```java
  // 读写分离
  @Transactional(readOnly = true)
  public SeckillProduct getProduct(Long id) {
      // 从只读库查询
  }
  
  // 分库分表
  // 商品表按productId分片
  // 订单表按userId分片
  ```

**3. Redis瓶颈**
- **问题**：单实例QPS有上限
- **优化**：
  ```java
  // Redis集群
  // 读写分离
  // 热点数据预加载
  public void preloadHotData() {
      // 秒杀开始前预热库存数据
      redisTemplate.opsForValue().set(stockKey, initialStock);
  }
  ```

**4. 应用层瓶颈**
- **问题**：业务逻辑复杂，处理时间长
- **优化**：
  ```java
  // 异步处理
  @Async
  public void processSecondaryBusiness(SeckillMessage message) {
      // 非核心业务异步处理
  }
  
  // 业务逻辑简化
  // 减少不必要的数据库查询
  // 使用缓存减少计算
  ```

**5. JVM瓶颈**
- **问题**：GC暂停影响响应时间
- **优化**：
  ```bash
  # G1GC调优
  -XX:+UseG1GC
  -XX:MaxGCPauseMillis=100
  -XX:+ParallelRefProcEnabled
  
  # 内存池优化
  -Xms4g -Xmx4g
  -XX:NewRatio=1
  ```

**性能测试结果：**
- **优化前**：500 QPS，平均响应时间 200ms
- **优化后**：5000 QPS，平均响应时间 50ms

---

### Q10: 如何设计秒杀系统的预热机制？

**回答示例：**

预热机制是秒杀系统性能优化的重要环节：

**1. 数据预热策略**
```java
@Component
public class SeckillPrewarmService {
    
    @Scheduled(cron = "0 0 9 * * ?") // 每天9点执行
    public void prewarmTodaysSeckill() {
        List<SeckillProduct> products = getTodaysSeckillProducts();
        
        for (SeckillProduct product : products) {
            // 预热库存数据到Redis
            redisStockService.initStock(product.getId(), product.getAvailableStock());
            
            // 预热商品信息到Redis
            redisTemplate.opsForValue().set(
                "product:" + product.getId(), 
                product, 
                1, TimeUnit.HOURS
            );
            
            // 预热到JVM本地缓存
            localCache.put("product:" + product.getId(), product);
        }
    }
}
```

**2. 连接池预热**
```java
@PostConstruct
public void prewarmConnections() {
    // 数据库连接池预热
    for (int i = 0; i < 10; i++) {
        CompletableFuture.runAsync(() -> {
            try (Connection conn = dataSource.getConnection()) {
                conn.prepareStatement("SELECT 1").executeQuery();
            }
        });
    }
    
    // Redis连接池预热
    redisTemplate.opsForValue().get("prewarm");
    
    // RocketMQ连接预热
    rocketMQTemplate.convertAndSend("prewarm_topic", "prewarm");
}
```

**3. JVM预热**
```java
public class JVMPrewarmService {
    
    @PostConstruct
    public void prewarmJVM() {
        // 预热核心类加载
        try {
            Class.forName("com.exp.miaoshademo.service.SeckillService");
            Class.forName("com.exp.miaoshademo.service.RedisStockService");
        } catch (ClassNotFoundException e) {
            log.error("JVM预热失败", e);
        }
        
        // 预热JIT编译
        for (int i = 0; i < 10000; i++) {
            simulateSeckillLogic();
        }
    }
    
    private void simulateSeckillLogic() {
        // 模拟秒杀核心逻辑，触发JIT编译
    }
}
```

**4. 缓存预热**
```java
public class CachePrewarmService {
    
    public void prewarmMultiLevelCache() {
        // L1缓存：本地Caffeine缓存
        Cache<String, Object> localCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();
            
        // L2缓存：Redis缓存
        // L3缓存：数据库
        
        // 预热热点数据到各级缓存
        List<Long> hotProductIds = getHotProductIds();
        for (Long productId : hotProductIds) {
            SeckillProduct product = databaseService.getProduct(productId);
            localCache.put("product:" + productId, product);
            redisTemplate.opsForValue().set("product:" + productId, product);
        }
    }
}
```

**5. 流量预热**
```java
public class TrafficPrewarmService {
    
    public void generatePrewarmTraffic() {
        // 模拟用户请求，预热整个链路
        ExecutorService executor = Executors.newFixedThreadPool(50);
        
        for (int i = 0; i < 1000; i++) {
            executor.submit(() -> {
                try {
                    // 模拟查询商品信息
                    seckillService.getProduct(1L);
                    
                    // 模拟获取库存
                    seckillService.getRedisStock(1L);
                    
                    Thread.sleep(10);
                } catch (Exception e) {
                    log.debug("预热请求异常", e);
                }
            });
        }
    }
}
```

**预热效果监控：**
```java
@Component
public class PrewarmMonitor {
    
    public void monitorPrewarmEffect() {
        // 监控缓存命中率
        double hitRate = cacheManager.getHitRate();
        
        // 监控响应时间
        double avgResponseTime = getAverageResponseTime();
        
        // 监控JVM状态
        MemoryUsage heapUsage = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage();
        
        log.info("预热效果 - 缓存命中率:{}, 平均响应时间:{}ms, 堆内存使用率:{}%", 
            hitRate, avgResponseTime, heapUsage.getUsed() * 100.0 / heapUsage.getMax());
    }
}
```

---

### Q11: 如何处理秒杀系统中的热点数据问题？

**回答示例：**

热点数据是秒杀系统面临的重大挑战，我采用了多层解决方案：

**1. 热点识别机制**
```java
@Component
public class HotspotDetector {
    
    private final Map<String, AtomicLong> accessCounter = new ConcurrentHashMap<>();
    
    @Scheduled(fixedRate = 10000) // 每10秒检测一次
    public void detectHotspot() {
        accessCounter.forEach((key, count) -> {
            if (count.get() > 1000) { // 10秒内访问超过1000次
                handleHotspot(key);
            }
        });
        accessCounter.clear(); // 重置计数器
    }
    
    public void recordAccess(String key) {
        accessCounter.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
    }
}
```

**2. 多级缓存架构**
```java
@Service
public class MultiLevelCacheService {
    
    // L1: 本地缓存 (Caffeine)
    private final Cache<String, Object> l1Cache = Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();
    
    // L2: Redis缓存
    // L3: 数据库
    
    public SeckillProduct getProduct(Long productId) {
        String key = "product:" + productId;
        
        // L1缓存查询
        SeckillProduct product = (SeckillProduct) l1Cache.getIfPresent(key);
        if (product != null) {
            return product;
        }
        
        // L2缓存查询
        product = (SeckillProduct) redisTemplate.opsForValue().get(key);
        if (product != null) {
            l1Cache.put(key, product); // 回写L1
            return product;
        }
        
        // L3数据库查询
        product = databaseService.getProduct(productId);
        if (product != null) {
            // 回写L2和L1
            redisTemplate.opsForValue().set(key, product, 10, TimeUnit.MINUTES);
            l1Cache.put(key, product);
        }
        
        return product;
    }
}
```

**3. 热点数据分片**
```java
public class HotspotShardingService {
    
    private static final int SHARD_COUNT = 10;
    
    public void setHotspotData(String key, Object value) {
        // 热点数据分片存储
        for (int i = 0; i < SHARD_COUNT; i++) {
            String shardKey = key + ":shard:" + i;
            redisTemplate.opsForValue().set(shardKey, value);
        }
    }
    
    public Object getHotspotData(String key) {
        // 随机选择分片读取，分散读压力
        int shardIndex = ThreadLocalRandom.current().nextInt(SHARD_COUNT);
        String shardKey = key + ":shard:" + shardIndex;
        return redisTemplate.opsForValue().get(shardKey);
    }
}
```

**4. 动态扩容机制**
```java
@Component
public class DynamicScalingService {
    
    public void handleHotspot(String hotspotKey) {
        // 动态增加Redis实例
        if (isRedisHotspot(hotspotKey)) {
            scaleRedisCluster();
        }
        
        // 动态增加应用实例
        if (isApplicationBottleneck()) {
            scaleApplicationInstances();
        }
        
        // 启用CDN加速
        if (isStaticResource(hotspotKey)) {
            enableCDNAcceleration(hotspotKey);
        }
    }
    
    private void scaleRedisCluster() {
        // 调用云服务API动态增加Redis节点
        cloudService.addRedisNode();
        
        // 重新分配数据分片
        reshardRedisData();
    }
}
```

**5. 流量削峰策略**
```java
public class TrafficShapingService {
    
    // 令牌桶限流
    private final RateLimiter rateLimiter = RateLimiter.create(1000.0);
    
    // 排队机制
    private final BlockingQueue<SeckillRequest> requestQueue = 
        new LinkedBlockingQueue<>(10000);
    
    public SeckillResult handleSeckillRequest(SeckillRequest request) {
        if (!rateLimiter.tryAcquire()) {
            return SeckillResult.fail("系统繁忙，请稍后再试");
        }
        
        // 加入排队队列
        if (!requestQueue.offer(request)) {
            return SeckillResult.fail("排队人数过多，请稍后再试");
        }
        
        return SeckillResult.success("已加入排队队列");
    }
    
    @Async
    public void processQueuedRequests() {
        while (true) {
            try {
                SeckillRequest request = requestQueue.take();
                processSeckillRequest(request);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
}
```

**6. 监控和告警**
```java
@Component
public class HotspotMonitor {
    
    @EventListener
    public void onHotspotDetected(HotspotEvent event) {
        // 记录热点事件
        log.warn("检测到热点数据: {}, 访问量: {}", event.getKey(), event.getAccessCount());
        
        // 发送告警
        alertService.sendAlert("热点数据告警", 
            "检测到热点数据: " + event.getKey());
        
        // 触发自动扩容
        dynamicScalingService.handleHotspot(event.getKey());
    }
}
```

---

## 5. 容灾与监控问题

### Q12: 秒杀系统的容灾方案是什么？

**回答示例：**

容灾是保证秒杀系统稳定运行的重要保障，我设计了多层次的容灾方案：

**1. 应用层容灾**
```java
@Component
public class DisasterRecoveryService {
    
    // 服务降级
    public SeckillResult seckillWithDegradation(SeckillRequest request) {
        try {
            return normalSeckillProcess(request);
        } catch (RedisException e) {
            log.warn("Redis异常，启用降级模式", e);
            return degradedSeckillProcess(request);
        } catch (DatabaseException e) {
            log.error("数据库异常，系统降级", e);
            return SeckillResult.fail("系统维护中，请稍后再试");
        }
    }
    
    // 降级模式：仅返回排队结果
    private SeckillResult degradedSeckillProcess(SeckillRequest request) {
        // 记录请求到本地队列
        localQueue.offer(request);
        return SeckillResult.success("已加入排队队列，请耐心等待");
    }
}
```

**2. 缓存层容灾**
```java
@Service
public class CacheDisasterRecovery {
    
    // Redis主从切换
    @EventListener
    public void onRedisMasterDown(RedisMasterDownEvent event) {
        log.error("Redis主节点宕机，开始主从切换");
        
        // 自动切换到从节点
        jedisConnectionFactory.setHostName(redisSlaveHost);
        jedisConnectionFactory.setPort(redisSlavePort);
        jedisConnectionFactory.afterPropertiesSet();
        
        // 发送告警
        alertService.sendAlert("Redis主节点宕机", "已自动切换到从节点");
    }
    
    // 本地缓存兜底
    public Object getDataWithFallback(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.warn("Redis访问失败，使用本地缓存兜底", e);
            return localCache.get(key);
        }
    }
}
```

**3. 数据库层容灾**
```java
@Configuration
public class DatabaseDisasterRecovery {
    
    // 读写分离配置
    @Bean
    @Primary
    public DataSource primaryDataSource() {
        return DataSourceBuilder.create()
            .url(primaryDbUrl)
            .username(primaryDbUsername)
            .password(primaryDbPassword)
            .build();
    }
    
    @Bean
    public DataSource secondaryDataSource() {
        return DataSourceBuilder.create()
            .url(secondaryDbUrl)
            .username(secondaryDbUsername)
            .password(secondaryDbPassword)
            .build();
    }
    
    // 数据库异常时自动切换
    @EventListener
    public void onDatabaseException(DatabaseExceptionEvent event) {
        if (event.isPrimaryDatabase()) {
            log.error("主数据库异常，切换到备库");
            switchToSecondaryDatabase();
        }
    }
}
```

**4. 消息队列容灾**
```java
public class MQDisasterRecovery {
    
    // RocketMQ集群容灾
    @Bean
    public RocketMQTemplate rocketMQTemplate() {
        RocketMQTemplate template = new RocketMQTemplate();
        
        // 配置多个NameServer地址
        template.setNamesrvAddr("nameserver1:9876;nameserver2:9876;nameserver3:9876");
        
        // 配置重试策略
        template.setRetryTimesWhenSendFailed(3);
        template.setRetryTimesWhenSendAsyncFailed(3);
        
        return template;
    }
    
    // 消息备份机制
    public void sendMessageWithBackup(String topic, Object message) {
        try {
            rocketMQTemplate.convertAndSend(topic, message);
        } catch (Exception e) {
            log.error("主MQ发送失败，写入备份存储", e);
            backupStorage.store(topic, message);
        }
    }
}
```

**5. 网络层容灾**
```java
@Component
public class NetworkDisasterRecovery {
    
    // 多机房部署
    @Value("${app.datacenter.regions}")
    private List<String> regions;
    
    // 智能路由
    public String selectBestRegion(String userLocation) {
        // 根据用户地理位置选择最近的数据中心
        return regions.stream()
            .min(Comparator.comparing(region -> 
                calculateDistance(userLocation, region)))
            .orElse(regions.get(0));
    }
    
    // 跨机房数据同步
    @Scheduled(fixedRate = 30000)
    public void syncDataAcrossRegions() {
        for (String region : regions) {
            if (!region.equals(currentRegion)) {
                syncDataToRegion(region);
            }
        }
    }
}
```

**6. 容灾演练和恢复**
```java
@Component
public class DisasterDrillService {
    
    // 定期容灾演练
    @Scheduled(cron = "0 0 2 * * SUN") // 每周日凌晨2点
    public void conductDisasterDrill() {
        log.info("开始容灾演练");
        
        // 模拟Redis故障
        simulateRedisFailure();
        
        // 验证自动切换
        verifyAutoFailover();
        
        // 恢复正常状态
        restoreNormalState();
        
        log.info("容灾演练完成");
    }
    
    // 快速恢复程序
    public void quickRecovery() {
        // 重启关键服务
        restartCriticalServices();
        
        // 重新加载配置
        reloadConfigurations();
        
        // 验证系统状态
        verifySystemHealth();
    }
}
```

**容灾指标监控：**
```java
@Component
public class DisasterRecoveryMetrics {
    
    // RTO (Recovery Time Objective) - 恢复时间目标
    private static final long MAX_RTO_SECONDS = 300; // 5分钟
    
    // RPO (Recovery Point Objective) - 恢复点目标
    private static final long MAX_RPO_SECONDS = 60; // 1分钟
    
    public void monitorRecoveryMetrics() {
        long currentRTO = calculateCurrentRTO();
        long currentRPO = calculateCurrentRPO();
        
        if (currentRTO > MAX_RTO_SECONDS) {
            alertService.sendAlert("RTO超标", "当前RTO: " + currentRTO + "秒");
        }
        
        if (currentRPO > MAX_RPO_SECONDS) {
            alertService.sendAlert("RPO超标", "当前RPO: " + currentRPO + "秒");
        }
    }
}
```

---

### Q13: 如何监控秒杀系统的关键指标？

**回答示例：**

监控是保证秒杀系统稳定运行的眼睛，我建立了全方位的监控体系：

**1. 业务指标监控**
```java
@Component
public class BusinessMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    
    // 秒杀成功率
    private final Counter seckillSuccessCounter;
    private final Counter seckillFailCounter;
    
    // 响应时间
    private final Timer seckillResponseTimer;
    
    // 并发用户数
    private final Gauge concurrentUsersGauge;
    
    public BusinessMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.seckillSuccessCounter = Counter.builder("seckill.success")
            .description("秒杀成功次数")
            .register(meterRegistry);
        this.seckillFailCounter = Counter.builder("seckill.fail")
            .description("秒杀失败次数")
            .register(meterRegistry);
        this.seckillResponseTimer = Timer.builder("seckill.response.time")
            .description("秒杀响应时间")
            .register(meterRegistry);
    }
    
    public void recordSeckillSuccess() {
        seckillSuccessCounter.increment();
    }
    
    public void recordSeckillFail(String reason) {
        seckillFailCounter.increment(Tags.of("reason", reason));
    }
    
    public Timer.Sample startTimer() {
        return Timer.start(meterRegistry);
    }
}
```

**2. 系统资源监控**
```java
@Component
public class SystemResourceMonitor {
    
    @Scheduled(fixedRate = 10000) // 每10秒收集一次
    public void collectSystemMetrics() {
        // CPU使用率
        double cpuUsage = getCpuUsage();
        meterRegistry.gauge("system.cpu.usage", cpuUsage);
        
        // 内存使用率
        MemoryUsage heapUsage = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage();
        double memoryUsage = (double) heapUsage.getUsed() / heapUsage.getMax() * 100;
        meterRegistry.gauge("system.memory.usage", memoryUsage);
        
        // GC指标
        List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();
        for (GarbageCollectorMXBean gcBean : gcBeans) {
            meterRegistry.gauge("system.gc.time", Tags.of("gc", gcBean.getName()), 
                gcBean.getCollectionTime());
            meterRegistry.gauge("system.gc.count", Tags.of("gc", gcBean.getName()), 
                gcBean.getCollectionCount());
        }
        
        // 线程数
        int threadCount = ManagementFactory.getThreadMXBean().getThreadCount();
        meterRegistry.gauge("system.thread.count", threadCount);
    }
}
```

**3. 中间件监控**
```java
@Component
public class MiddlewareMonitor {
    
    // Redis监控
    public void monitorRedis() {
        try (Jedis jedis = jedisPool.getResource()) {
            String info = jedis.info();
            
            // 解析Redis INFO信息
            Map<String, String> redisInfo = parseRedisInfo(info);
            
            // 连接数
            meterRegistry.gauge("redis.connected.clients", 
                Double.parseDouble(redisInfo.get("connected_clients")));
            
            // 内存使用
            meterRegistry.gauge("redis.used.memory", 
                Double.parseDouble(redisInfo.get("used_memory")));
            
            // 命令执行数
            meterRegistry.gauge("redis.total.commands.processed", 
                Double.parseDouble(redisInfo.get("total_commands_processed")));
            
            // 键空间命中率
            double hits = Double.parseDouble(redisInfo.get("keyspace_hits"));
            double misses = Double.parseDouble(redisInfo.get("keyspace_misses"));
            double hitRate = hits / (hits + misses) * 100;
            meterRegistry.gauge("redis.keyspace.hit.rate", hitRate);
        }
    }
    
    // 数据库监控
    @EventListener
    public void onDatabaseQuery(DatabaseQueryEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("database.query.time")
            .tag("sql.type", event.getSqlType())
            .register(meterRegistry));
        
        // 慢查询监控
        if (event.getExecutionTime() > 1000) { // 超过1秒的查询
            Counter.builder("database.slow.query")
                .tag("sql", event.getSql())
                .register(meterRegistry)
                .increment();
        }
    }
    
    // RocketMQ监控
    @Scheduled(fixedRate = 30000)
    public void monitorRocketMQ() {
        // 生产者指标
        meterRegistry.gauge("rocketmq.producer.send.success", 
            rocketMQTemplate.getAsyncSendCountTotal());
        meterRegistry.gauge("rocketmq.producer.send.fail", 
            rocketMQTemplate.getAsyncSendFailCountTotal());
        
        // 消费者指标
        meterRegistry.gauge("rocketmq.consumer.message.count", 
            getConsumerMessageCount());
        meterRegistry.gauge("rocketmq.consumer.lag", 
            getConsumerLag());
    }
}
```

**4. 业务流程监控**
```java
@Aspect
@Component
public class BusinessFlowMonitor {
    
    @Around("@annotation(com.exp.miaoshademo.annotation.MonitorBusiness)")
    public Object monitorBusinessFlow(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            Object result = joinPoint.proceed();
            
            // 记录成功指标
            Counter.builder("business.flow.success")
                .tag("method", methodName)
                .register(meterRegistry)
                .increment();
            
            return result;
        } catch (Exception e) {
            // 记录失败指标
            Counter.builder("business.flow.error")
                .tag("method", methodName)
                .tag("error", e.getClass().getSimpleName())
                .register(meterRegistry)
                .increment();
            
            throw e;
        } finally {
            sample.stop(Timer.builder("business.flow.time")
                .tag("method", methodName)
                .register(meterRegistry));
        }
    }
}
```

**5. 告警机制**
```java
@Component
public class AlertManager {
    
    // 阈值配置
    private static final double CPU_THRESHOLD = 80.0;
    private static final double MEMORY_THRESHOLD = 85.0;
    private static final double ERROR_RATE_THRESHOLD = 5.0;
    private static final long RESPONSE_TIME_THRESHOLD = 1000;
    
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkAlerts() {
        // CPU使用率告警
        double cpuUsage = getCpuUsage();
        if (cpuUsage > CPU_THRESHOLD) {
            sendAlert(AlertLevel.HIGH, "CPU使用率过高", 
                String.format("当前CPU使用率: %.2f%%", cpuUsage));
        }
        
        // 错误率告警
        double errorRate = calculateErrorRate();
        if (errorRate > ERROR_RATE_THRESHOLD) {
            sendAlert(AlertLevel.CRITICAL, "错误率过高", 
                String.format("当前错误率: %.2f%%", errorRate));
        }
        
        // 响应时间告警
        double avgResponseTime = getAverageResponseTime();
        if (avgResponseTime > RESPONSE_TIME_THRESHOLD) {
            sendAlert(AlertLevel.MEDIUM, "响应时间过长", 
                String.format("平均响应时间: %.0fms", avgResponseTime));
        }
    }
    
    private void sendAlert(AlertLevel level, String title, String message) {
        AlertMessage alert = AlertMessage.builder()
            .level(level)
            .title(title)
            .message(message)
            .timestamp(LocalDateTime.now())
            .build();
        
        // 发送邮件告警
        emailService.sendAlert(alert);
        
        // 发送钉钉告警
        dingTalkService.sendAlert(alert);
        
        // 发送短信告警（高级别）
        if (level == AlertLevel.CRITICAL) {
            smsService.sendAlert(alert);
        }
    }
}
```

**6. 监控大盘**
```java
@RestController
@RequestMapping("/monitor")
public class MonitorDashboardController {
    
    @GetMapping("/dashboard")
    public MonitorDashboard getDashboard() {
        return MonitorDashboard.builder()
            .systemMetrics(getSystemMetrics())
            .businessMetrics(getBusinessMetrics())
            .middlewareMetrics(getMiddlewareMetrics())
            .alerts(getActiveAlerts())
            .build();
    }
    
    @GetMapping("/realtime")
    public ResponseEntity<SseEmitter> getRealTimeMetrics() {
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        
        // 每秒推送实时数据
        scheduledExecutor.scheduleAtFixedRate(() -> {
            try {
                RealTimeMetrics metrics = collectRealTimeMetrics();
                emitter.send(SseEmitter.event()
                    .name("metrics")
                    .data(metrics));
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        }, 0, 1, TimeUnit.SECONDS);
        
        return ResponseEntity.ok(emitter);
    }
}
```

---

### Q14: 如何进行秒杀系统的压力测试？

**回答示例：**

压力测试是验证秒杀系统性能的重要手段，我建立了完整的测试体系：

**1. 测试环境准备**
```java
@TestConfiguration
public class StressTestConfig {
    
    @Bean
    @Profile("stress-test")
    public TestDataInitializer testDataInitializer() {
        return new TestDataInitializer() {
            
            @Override
            public void initializeTestData() {
                // 初始化测试商品
                for (int i = 1; i <= 10; i++) {
                    SeckillProduct product = new SeckillProduct();
                    product.setId((long) i);
                    product.setProductName("测试商品" + i);
                    product.setTotalStock(1000);
                    product.setAvailableStock(1000);
                    product.setSeckillPrice(new BigDecimal("99.99"));
                    product.setStatus(1);
                    productService.save(product);
                    
                    // 初始化Redis库存
                    redisStockService.initStock((long) i, 1000);
                }
                
                // 初始化测试用户
                for (int i = 1; i <= 10000; i++) {
                    // 创建测试用户数据
                    createTestUser(i);
                }
            }
        };
    }
}
```

**2. JMeter压力测试脚本**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="秒杀系统压力测试">
      <elementProp name="TestPlan.arguments" elementType="Arguments">
        <collectionProp name="Arguments.arguments">
          <elementProp name="" elementType="HTTPArgument">
            <boolProp name="HTTPArgument.always_encode">false</boolProp>
            <stringProp name="Argument.value">${__P(host,localhost)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <boolProp name="HTTPArgument.use_equals">true</boolProp>
            <stringProp name="Argument.name">host</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
    </TestPlan>
    
    <hashTree>
      <!-- 线程组配置 -->
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="秒杀用户">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">10</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">1000</stringProp>
        <stringProp name="ThreadGroup.ramp_time">10</stringProp>
      </ThreadGroup>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

**3. 自动化压力测试工具**
```java
@Component
public class AutoStressTester {
    
    private final RestTemplate restTemplate;
    private final ExecutorService executorService;
    
    public StressTestResult runStressTest(StressTestConfig config) {
        log.info("开始压力测试 - 并发用户数: {}, 测试时长: {}秒", 
            config.getConcurrentUsers(), config.getDurationSeconds());
        
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(config.getConcurrentUsers());
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong failCount = new AtomicLong(0);
        List<Long> responseTimes = new CopyOnWriteArrayList<>();
        
        // 创建并发用户
        for (int i = 0; i < config.getConcurrentUsers(); i++) {
            final int userId = i + 1;
            executorService.submit(() -> {
                try {
                    startLatch.await(); // 等待统一开始信号
                    
                    long startTime = System.currentTimeMillis();
                    SeckillResult result = performSeckill(userId, config.getProductId());
                    long endTime = System.currentTimeMillis();
                    
                    responseTimes.add(endTime - startTime);
                    
                    if (result.isSuccess()) {
                        successCount.incrementAndGet();
                    } else {
                        failCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    failCount.incrementAndGet();
                } finally {
                    endLatch.countDown();
                }
            });
        }
        
        // 开始测试
        long testStartTime = System.currentTimeMillis();
        startLatch.countDown();
        
        try {
            endLatch.await(config.getDurationSeconds(), TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long testEndTime = System.currentTimeMillis();
        
        return buildTestResult(testStartTime, testEndTime, successCount.get(), 
            failCount.get(), responseTimes);
    }
    
    private SeckillResult performSeckill(int userId, Long productId) {
        String url = "http://localhost:8080/seckill/doSeckill";
        
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("userId", String.valueOf(userId));
        params.add("productId", String.valueOf(productId));
        params.add("quantity", "1");
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
        
        ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
        Map<String, Object> body = response.getBody();
        
        boolean success = (Boolean) body.get("success");
        String message = (String) body.get("message");
        
        return success ? SeckillResult.success(null, null) : SeckillResult.fail(message);
    }
}
```

**4. 性能指标收集**
```java
@Component
public class PerformanceMetricsCollector {
    
    public PerformanceMetrics collectMetrics() {
        return PerformanceMetrics.builder()
            .qps(calculateQPS())
            .avgResponseTime(calculateAvgResponseTime())
            .p95ResponseTime(calculateP95ResponseTime())
            .p99ResponseTime(calculateP99ResponseTime())
            .errorRate(calculateErrorRate())
            .cpuUsage(getCpuUsage())
            .memoryUsage(getMemoryUsage())
            .gcTime(getGcTime())
            .build();
    }
    
    private double calculateQPS() {
        // 计算每秒请求数
        long totalRequests = meterRegistry.counter("http.requests").count();
        long testDuration = getTestDuration();
        return (double) totalRequests / testDuration;
    }
    
    private double calculateP95ResponseTime() {
        Timer timer = meterRegistry.timer("http.request.duration");
        return timer.percentile(0.95, TimeUnit.MILLISECONDS);
    }
    
    private double calculateErrorRate() {
        long totalRequests = meterRegistry.counter("http.requests").count();
        long errorRequests = meterRegistry.counter("http.requests", "status", "5xx").count();
        return totalRequests > 0 ? (double) errorRequests / totalRequests * 100 : 0;
    }
}
```

**5. 压测结果分析**
```java
@Service
public class StressTestAnalyzer {
    
    public TestAnalysisReport analyzeTestResult(StressTestResult result) {
        TestAnalysisReport report = new TestAnalysisReport();
        
        // 基础指标分析
        report.setQps(result.getTotalRequests() / result.getDurationSeconds());
        report.setSuccessRate((double) result.getSuccessCount() / result.getTotalRequests() * 100);
        report.setAvgResponseTime(calculateAverage(result.getResponseTimes()));
        
        // 响应时间分布分析
        List<Long> sortedTimes = result.getResponseTimes().stream()
            .sorted()
            .collect(Collectors.toList());
        
        report.setP50ResponseTime(getPercentile(sortedTimes, 0.5));
        report.setP90ResponseTime(getPercentile(sortedTimes, 0.9));
        report.setP95ResponseTime(getPercentile(sortedTimes, 0.95));
        report.setP99ResponseTime(getPercentile(sortedTimes, 0.99));
        
        // 性能评级
        report.setPerformanceGrade(calculatePerformanceGrade(report));
        
        // 瓶颈分析
        report.setBottlenecks(identifyBottlenecks(result));
        
        // 优化建议
        report.setOptimizationSuggestions(generateOptimizationSuggestions(report));
        
        return report;
    }
    
    private PerformanceGrade calculatePerformanceGrade(TestAnalysisReport report) {
        if (report.getQps() > 5000 && report.getP95ResponseTime() < 100) {
            return PerformanceGrade.EXCELLENT;
        } else if (report.getQps() > 3000 && report.getP95ResponseTime() < 200) {
            return PerformanceGrade.GOOD;
        } else if (report.getQps() > 1000 && report.getP95ResponseTime() < 500) {
            return PerformanceGrade.FAIR;
        } else {
            return PerformanceGrade.POOR;
        }
    }
    
    private List<String> identifyBottlenecks(StressTestResult result) {
        List<String> bottlenecks = new ArrayList<>();
        
        // CPU瓶颈
        if (result.getMaxCpuUsage() > 90) {
            bottlenecks.add("CPU使用率过高，建议增加实例或优化算法");
        }
        
        // 内存瓶颈
        if (result.getMaxMemoryUsage() > 85) {
            bottlenecks.add("内存使用率过高，建议优化内存使用或增加堆内存");
        }
        
        // 数据库瓶颈
        if (result.getDbSlowQueryCount() > 100) {
            bottlenecks.add("数据库慢查询过多，建议优化SQL或增加索引");
        }
        
        // Redis瓶颈
        if (result.getRedisConnections() > 1000) {
            bottlenecks.add("Redis连接数过多，建议使用连接池或Redis集群");
        }
        
        return bottlenecks;
    }
}
```

**6. 持续集成中的压测**
```yaml
# .github/workflows/stress-test.yml
name: Stress Test

on:
  schedule:
    - cron: '0 2 * * *' # 每天凌晨2点执行
  workflow_dispatch:

jobs:
  stress-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v2
      
    - name: Setup Java
      uses: actions/setup-java@v2
      with:
        java-version: '17'
        
    - name: Start test environment
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30 # 等待服务启动
        
    - name: Run stress test
      run: |
        mvn test -Dtest=StressTest -Dspring.profiles.active=stress-test
        
    - name: Generate test report
      run: |
        java -jar stress-test-reporter.jar --input target/stress-test-results.json --output stress-test-report.html
        
    - name: Upload test report
      uses: actions/upload-artifact@v2
      with:
        name: stress-test-report
        path: stress-test-report.html
        
    - name: Send notification
      if: failure()
      run: |
        curl -X POST ${{ secrets.SLACK_WEBHOOK }} \
          -H 'Content-type: application/json' \
          --data '{"text":"Stress test failed! Check the report for details."}'
```

---

### Q15: 面对千万级并发，你会如何设计秒杀系统？

**回答示例：**

千万级并发是极端场景，需要从架构、技术栈、运维等多个维度进行全面设计：

**1. 分层架构设计**
```
                                [用户]
                                  |
                            [CDN + DNS智能解析]
                                  |
                          [API网关 + 负载均衡集群]
                                  |
                        [应用服务集群 (1000+ 实例)]
                                  |
              [缓存层集群]    [消息队列集群]    [存储层集群]
                Redis          RocketMQ         MySQL/MongoDB
               (主从+分片)       (多机房)        (分库分表)
```

**2. 核心技术选型**
```java
// 技术栈配置
@Configuration
public class UltraHighConcurrencyConfig {
    
    // 应用层：Spring Boot + Netty
    @Bean
    public NettyReactiveWebServerFactory nettyReactiveWebServerFactory() {
        NettyReactiveWebServerFactory factory = new NettyReactiveWebServerFactory();
        factory.addServerCustomizers(server -> {
            server.option(ChannelOption.SO_BACKLOG, 65536)
                  .option(ChannelOption.SO_REUSEADDR, true)
                  .childOption(ChannelOption.SO_KEEPALIVE, true)
                  .childOption(ChannelOption.TCP_NODELAY, true);
        });
        return factory;
    }
    
    // 缓存层：Redis集群 + 本地缓存
    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        // 配置100+节点的Redis集群
        for (int i = 0; i < 100; i++) {
            clusterConfig.clusterNode("redis-node-" + i, 6379);
        }
        
        LettucePoolingClientConfiguration poolConfig = 
            LettucePoolingClientConfiguration.builder()
                .poolConfig(GenericObjectPoolConfig.builder()
                    .maxTotal(1000)
                    .maxIdle(100)
                    .minIdle(10)
                    .build())
                .build();
                
        return new LettuceConnectionFactory(clusterConfig, poolConfig);
    }
}
```

**3. 极致性能优化**
```java
@Service
public class UltraHighPerformanceSeckillService {
    
    // 零拷贝技术
    @Autowired
    private DirectMemoryService directMemoryService;
    
    // 协程异步处理
    public Mono<SeckillResult> seckillAsync(SeckillRequest request) {
        return Mono.fromCallable(() -> {
            // 使用直接内存减少GC
            ByteBuffer buffer = directMemoryService.allocate(1024);
            
            try {
                // 预编译的Lua脚本，减少网络传输
                String scriptSha = precompiledScriptSha;
                
                // 批量处理，减少网络往返
                return reactiveRedisTemplate
                    .execute(connection -> connection.evalSha(scriptSha, 
                        ReturnType.MULTI, keys, args))
                    .cast(List.class)
                    .map(this::parseResult)
                    .block();
            } finally {
                directMemoryService.release(buffer);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .timeout(Duration.ofMillis(100)); // 超时保护
    }
    
    // 内存池复用
    private final ObjectPool<SeckillContext> contextPool = 
        new GenericObjectPool<>(new SeckillContextFactory());
    
    public SeckillResult processWithPool(SeckillRequest request) {
        SeckillContext context = null;
        try {
            context = contextPool.borrowObject();
            context.setRequest(request);
            return context.process();
        } finally {
            if (context != null) {
                contextPool.returnObject(context);
            }
        }
    }
}
```

**4. 分布式架构**
```java
// 多机房部署
@Configuration
public class MultiDataCenterConfig {
    
    // 智能路由
    @Bean
    public DataCenterRouter dataCenterRouter() {
        return new DataCenterRouter() {
            @Override
            public String selectDataCenter(String userRegion) {
                // 基于用户地理位置和机房负载进行智能路由
                return dataCenters.stream()
                    .filter(dc -> dc.getRegion().equals(userRegion))
                    .min(Comparator.comparing(DataCenter::getCurrentLoad))
                    .map(DataCenter::getName)
                    .orElse(defaultDataCenter);
            }
        };
    }
    
    // 跨机房数据同步
    @Component
    public class CrossDataCenterSync {
        
        @EventListener
        public void onSeckillSuccess(SeckillSuccessEvent event) {
            // 异步同步到其他机房
            otherDataCenters.parallelStream().forEach(dc -> {
                CompletableFuture.runAsync(() -> {
                    syncToDataCenter(dc, event);
                });
            });
        }
    }
}

// 微服务拆分
@FeignClient(name = "stock-service")
public interface StockService {
    @PostMapping("/stock/deduct")
    StockResult deductStock(@RequestBody StockRequest request);
}

@FeignClient(name = "order-service")
public interface OrderService {
    @PostMapping("/order/create")
    OrderResult createOrder(@RequestBody OrderRequest request);
}
```

**5. 极端优化策略**
```java
@Component
public class ExtremeOptimizationService {
    
    // 预计算和预分配
    @PostConstruct
    public void precompute() {
        // 预计算所有可能的订单号
        for (int i = 0; i < 1000000; i++) {
            precomputedOrderNumbers.add(generateOrderNumber());
        }
        
        // 预分配对象池
        for (int i = 0; i < 10000; i++) {
            objectPool.addObject(new SeckillContext());
        }
    }
    
    // 批量处理
    @Scheduled(fixedRate = 10) // 每10ms处理一批
    public void batchProcess() {
        List<SeckillRequest> batch = requestQueue.drainTo(1000);
        if (!batch.isEmpty()) {
            processBatch(batch);
        }
    }
    
    // CPU亲和性绑定
    public void bindCpuAffinity() {
        // 将关键线程绑定到特定CPU核心
        ThreadAffinityExecutor executor = new ThreadAffinityExecutor();
        executor.bindToCore(Thread.currentThread(), 0);
    }
    
    // 内存映射文件
    public void useMemoryMappedFile() {
        try (RandomAccessFile file = new RandomAccessFile("seckill.dat", "rw");
             MappedByteBuffer buffer = file.getChannel().map(
                 FileChannel.MapMode.READ_WRITE, 0, file.length())) {
            
            // 直接操作内存映射区域，避免用户态到内核态切换
            buffer.putLong(productId);
            buffer.putInt(stock);
        }
    }
}
```

**6. 运维和监控**
```java
// 自动扩缩容
@Component
public class AutoScalingService {
    
    @Scheduled(fixedRate = 10000)
    public void autoScale() {
        double currentLoad = getCurrentSystemLoad();
        
        if (currentLoad > 0.8) {
            // 自动扩容
            scaleOut();
        } else if (currentLoad < 0.3) {
            // 自动缩容
            scaleIn();
        }
    }
    
    private void scaleOut() {
        // 调用云服务API创建新实例
        cloudService.createInstances(10);
        
        // 更新负载均衡配置
        loadBalancer.addServers(newServers);
        
        // 预热新实例
        prewarmService.prewarmNewInstances(newServers);
    }
}

// 混沌工程
@Component
public class ChaosEngineeringService {
    
    @Scheduled(cron = "0 0 3 * * ?")
    public void runChaosTest() {
        // 随机关闭部分实例
        shutdownRandomInstances(0.1); // 关闭10%的实例
        
        // 模拟网络延迟
        introduceNetworkLatency(100); // 增加100ms延迟
        
        // 验证系统恢复能力
        verifySystemResilience();
    }
}
```

**7. 成本优化**
```java
// 弹性资源管理
@Component
public class CostOptimizationService {
    
    // 基于预测的资源调度
    public void predictiveScaling() {
        // 根据历史数据预测流量高峰
        TrafficPrediction prediction = aiService.predictTraffic();
        
        // 提前准备资源
        scheduleResourcePreparation(prediction);
    }
    
    // 竞价实例使用
    public void useSpotInstances() {
        // 使用云服务商的竞价实例降低成本
        List<SpotInstance> spotInstances = cloudService.launchSpotInstances(
            instanceConfig, maxPrice);
            
        // 混合使用按需实例和竞价实例
        balanceInstanceTypes();
    }
}
```

**架构承载能力预估：**
- **QPS容量**: 1000万+ (通过水平扩展)
- **响应时间**: P99 < 50ms
- **可用性**: 99.99%+ (4个9)
- **数据一致性**: 最终一致性 < 1秒
- **成本**: 相比传统架构节省60%+

这样的架构可以支撑双11、618等大型促销活动的流量冲击。

---

## 总结

这15个问题涵盖了秒杀系统的核心技术点：

1. **基础架构**：Redis、RocketMQ、MySQL的合理使用
2. **分布式事务**：CAP理论的权衡和最终一致性保证
3. **性能优化**：从代码层到架构层的全方位优化
4. **高可用设计**：容灾、监控、告警的完整体系
5. **极限场景**：千万级并发的架构设计思路

面试时要结合具体项目经验，用数据说话，展现对技术原理的深度理解和实际应用能力。