package com.exp.miaoshademo.service;

import com.exp.miaoshademo.config.RocketMQConfig;
import com.exp.miaoshademo.dto.SeckillMessage;
import com.exp.miaoshademo.entity.SeckillOrder;
import com.exp.miaoshademo.entity.StockLog;
import com.exp.miaoshademo.entity.UserSeckillRecord;
import com.exp.miaoshademo.mapper.SeckillOrderMapper;
import com.exp.miaoshademo.mapper.StockLogMapper;
import com.exp.miaoshademo.mapper.UserSeckillRecordMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Service
@RocketMQMessageListener(
    topic = RocketMQConfig.SECKILL_TOPIC,
    consumerGroup = RocketMQConfig.SECKILL_GROUP,
    selectorExpression = RocketMQConfig.SECKILL_TAG
)
@RequiredArgsConstructor
@Slf4j
public class SeckillMessageConsumer implements RocketMQListener<String> {

    private final ObjectMapper objectMapper;
    private final RocketMQProducerService mqProducerService;
    private final SeckillOrderMapper orderMapper;
    private final StockLogMapper stockLogMapper;
    private final UserSeckillRecordMapper userSeckillRecordMapper;
    private final DatabaseStockService databaseStockService;

    @Override
    public void onMessage(String message) {
        log.info("收到秒杀消息，开始执行MySQL数据库操作: {}", message);
        
        try {
            SeckillMessage seckillMessage = objectMapper.readValue(message, SeckillMessage.class);
            
            executeDatabaseTransaction(seckillMessage);
            
            log.info("MySQL数据库操作完成 - 订单号:{}, 用户:{}, 商品:{}", 
                seckillMessage.getOrderNo(), seckillMessage.getUserId(), seckillMessage.getProductId());
            
        } catch (Exception e) {
            log.error("处理秒杀消息失败: {}", message, e);
            throw new RuntimeException("数据库操作失败", e);
        }
    }
    
    @Transactional(rollbackFor = Exception.class)
    public void executeDatabaseTransaction(SeckillMessage seckillMessage) {
        log.info("执行数据库事务 - 订单号:{}", seckillMessage.getOrderNo());
        
        SeckillOrder order = new SeckillOrder();
        order.setOrderNo(seckillMessage.getOrderNo());
        order.setUserId(seckillMessage.getUserId());
        order.setProductId(seckillMessage.getProductId());
        order.setProductName(seckillMessage.getProductName());
        order.setSeckillPrice(seckillMessage.getSeckillPrice());
        order.setQuantity(seckillMessage.getQuantity());
        order.setTotalAmount(seckillMessage.getSeckillPrice().multiply(BigDecimal.valueOf(seckillMessage.getQuantity())));
        order.setStatus(0);
        order.setCreatedTime(LocalDateTime.now());
        order.setUpdatedTime(LocalDateTime.now());
        
        orderMapper.insert(order);
        log.info("创建订单成功 - 订单号:{}", seckillMessage.getOrderNo());
        
        boolean stockUpdated = databaseStockService.deductStock(
            seckillMessage.getProductId(), 
            seckillMessage.getQuantity(),
            seckillMessage.getBeforeStock()
        );
        
        if (!stockUpdated) {
            log.error("数据库库存扣减失败 - 商品:{}, 扣减数量:{}", 
                seckillMessage.getProductId(), seckillMessage.getQuantity());
            throw new RuntimeException("数据库库存扣减失败");
        }
        
        StockLog stockLog = new StockLog();
        stockLog.setProductId(seckillMessage.getProductId());
        stockLog.setUserId(seckillMessage.getUserId());
        stockLog.setOrderNo(seckillMessage.getOrderNo());
        stockLog.setChangeAmount(-seckillMessage.getQuantity());
        stockLog.setBeforeStock(seckillMessage.getBeforeStock());
        stockLog.setAfterStock(seckillMessage.getAfterStock());
        stockLog.setOperationType(1);
        stockLog.setStatus(1);
        stockLog.setCreatedTime(LocalDateTime.now());
        stockLog.setUpdatedTime(LocalDateTime.now());
        
        stockLogMapper.insert(stockLog);
        
        UserSeckillRecord userRecord = new UserSeckillRecord();
        userRecord.setUserId(seckillMessage.getUserId());
        userRecord.setProductId(seckillMessage.getProductId());
        userRecord.setOrderNo(seckillMessage.getOrderNo());
        userRecord.setSeckillTime(LocalDateTime.now());
        userRecord.setStatus(1);
        userRecord.setCreatedTime(LocalDateTime.now());
        userRecord.setUpdatedTime(LocalDateTime.now());
        
        userSeckillRecordMapper.insert(userRecord);
        
        log.info("数据库事务执行成功 - 订单号:{}", seckillMessage.getOrderNo());
    }
}