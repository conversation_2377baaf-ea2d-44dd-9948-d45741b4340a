package com.exp.miaoshademo;

import com.exp.miaoshademo.service.SeckillService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

@SpringBootTest
@Slf4j
public class SeckillConcurrentTest {

    @Autowired
    private SeckillService seckillService;

    @Test
    public void testConcurrentSeckill() throws InterruptedException {
        Long productId = 1L;
        int threadCount = 100;
        int quantity = 1;
        
        seckillService.initProductStock(productId);
        
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(50);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        for (int i = 0; i < threadCount; i++) {
            Long userId = (long) (i + 1);
            
            executor.submit(() -> {
                try {
                    SeckillService.SeckillResult result = seckillService.doSeckill(userId, productId, quantity);
                    
                    if (result.isSuccess()) {
                        successCount.incrementAndGet();
                        log.info("秒杀成功 - 用户:{}, 订单号:{}, 剩余库存:{}", 
                            userId, result.getOrderNo(), result.getRemainStock());
                    } else {
                        failCount.incrementAndGet();
                        log.debug("秒杀失败 - 用户:{}, 原因:{}", userId, result.getMessage());
                    }
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    log.error("秒杀异常 - 用户:{}", userId, e);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        log.info("并发测试完成 - 总请求数:{}, 成功:{}, 失败:{}", 
            threadCount, successCount.get(), failCount.get());
        
        Integer finalRedisStock = seckillService.getRedisStock(productId);
        log.info("最终Redis库存:{}", finalRedisStock);
    }

    @Test
    public void testSeckillWithSameUser() throws InterruptedException {
        Long productId = 2L;
        Long userId = 1000L;
        int threadCount = 10;
        int quantity = 1;
        
        seckillService.initProductStock(productId);
        
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(10);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    SeckillService.SeckillResult result = seckillService.doSeckill(userId, productId, quantity);
                    
                    if (result.isSuccess()) {
                        successCount.incrementAndGet();
                        log.info("秒杀成功 - 用户:{}, 订单号:{}", userId, result.getOrderNo());
                    } else {
                        failCount.incrementAndGet();
                        log.debug("秒杀失败 - 用户:{}, 原因:{}", userId, result.getMessage());
                    }
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    log.error("秒杀异常 - 用户:{}", userId, e);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        log.info("同一用户并发测试完成 - 总请求数:{}, 成功:{}, 失败:{}", 
            threadCount, successCount.get(), failCount.get());
        
        if (successCount.get() > 1) {
            log.error("同一用户秒杀成功次数超过1次，存在重复购买问题！");
        } else {
            log.info("同一用户防重复购买机制正常");
        }
    }
}