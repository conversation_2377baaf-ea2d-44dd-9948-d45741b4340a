package com.exp.miaoshademo.service;

import com.exp.miaoshademo.config.OrderCloseConfig;
import com.exp.miaoshademo.dto.OrderCloseTask;
import com.exp.miaoshademo.entity.SeckillOrder;
import com.exp.miaoshademo.enums.OrderStatus;
import com.exp.miaoshademo.mapper.SeckillOrderMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrderCloseServiceTest {
    
    @Mock
    private SeckillOrderMapper orderMapper;
    
    @Mock
    private RedisStockService redisStockService;
    
    @Mock
    private DatabaseStockService databaseStockService;
    
    private OrderCloseConfig config;
    private BlockingQueue<OrderCloseTask> orderCloseQueue;
    private OrderCloseConsumer orderCloseConsumer;
    
    @BeforeEach
    void setUp() {
        config = new OrderCloseConfig();
        config.setEnabled(true);
        config.setTimeoutMinutes(30);
        config.setMaxRetryCount(3);
        
        orderCloseQueue = new LinkedBlockingQueue<>(100);
        
        orderCloseConsumer = new OrderCloseConsumer(
                orderMapper, null, redisStockService, 
                databaseStockService, config, orderCloseQueue
        );
    }
    
    @Test
    void testCloseOrderSuccess() {
        // 准备测试数据
        OrderCloseTask task = createTestOrderCloseTask();
        SeckillOrder order = createTestSeckillOrder();
        
        // Mock 行为
        when(orderMapper.selectById(task.getOrderId())).thenReturn(order);
        when(orderMapper.update(any(), any())).thenReturn(1);
        when(databaseStockService.restoreStock(anyLong(), anyInt())).thenReturn(true);
        doNothing().when(redisStockService).restoreStock(anyLong(), anyInt());
        
        // 执行测试
        boolean result = orderCloseConsumer.closeOrder(task);
        
        // 验证结果
        assertTrue(result);
        verify(orderMapper).selectById(task.getOrderId());
        verify(orderMapper).update(any(), any());
        verify(redisStockService).restoreStock(task.getProductId(), task.getQuantity());
        verify(databaseStockService).restoreStock(task.getProductId(), task.getQuantity());
    }
    
    @Test
    void testCloseOrderWhenOrderNotExists() {
        // 准备测试数据
        OrderCloseTask task = createTestOrderCloseTask();
        
        // Mock 行为
        when(orderMapper.selectById(task.getOrderId())).thenReturn(null);
        
        // 执行测试
        boolean result = orderCloseConsumer.closeOrder(task);
        
        // 验证结果
        assertFalse(result);
        verify(orderMapper).selectById(task.getOrderId());
        verify(orderMapper, never()).update(any(), any());
    }
    
    @Test
    void testCloseOrderWhenOrderAlreadyPaid() {
        // 准备测试数据
        OrderCloseTask task = createTestOrderCloseTask();
        SeckillOrder order = createTestSeckillOrder();
        order.setStatus(OrderStatus.PAID.getCode()); // 已支付状态
        
        // Mock 行为
        when(orderMapper.selectById(task.getOrderId())).thenReturn(order);
        
        // 执行测试
        boolean result = orderCloseConsumer.closeOrder(task);
        
        // 验证结果
        assertTrue(result); // 认为成功，因为订单已经不是待支付状态
        verify(orderMapper).selectById(task.getOrderId());
        verify(orderMapper, never()).update(any(), any());
    }
    
    @Test
    void testOrderStatusEnum() {
        // 测试订单状态枚举
        assertTrue(OrderStatus.isPendingPayment(0));
        assertFalse(OrderStatus.isPendingPayment(1));
        
        assertTrue(OrderStatus.canClose(0));
        assertFalse(OrderStatus.canClose(1));
        
        assertEquals(OrderStatus.PENDING_PAYMENT, OrderStatus.fromCode(0));
        assertEquals(OrderStatus.PAID, OrderStatus.fromCode(1));
        assertEquals(OrderStatus.CANCELLED, OrderStatus.fromCode(2));
        assertEquals(OrderStatus.REFUNDED, OrderStatus.fromCode(3));
    }
    
    private OrderCloseTask createTestOrderCloseTask() {
        return new OrderCloseTask(
                1L,                                    // orderId
                "SK20250101120000123456001",          // orderNo
                1001L,                                // userId
                1L,                                   // productId
                "iPhone 15 Pro",                      // productName
                1,                                    // quantity
                new BigDecimal("6999.00"),            // seckillPrice
                new BigDecimal("6999.00"),            // totalAmount
                OrderStatus.PENDING_PAYMENT.getCode(), // status
                LocalDateTime.now().minusMinutes(35)   // createdTime (超时)
        );
    }
    
    private SeckillOrder createTestSeckillOrder() {
        SeckillOrder order = new SeckillOrder();
        order.setId(1L);
        order.setOrderNo("SK20250101120000123456001");
        order.setUserId(1001L);
        order.setProductId(1L);
        order.setProductName("iPhone 15 Pro");
        order.setQuantity(1);
        order.setSeckillPrice(new BigDecimal("6999.00"));
        order.setTotalAmount(new BigDecimal("6999.00"));
        order.setStatus(OrderStatus.PENDING_PAYMENT.getCode());
        order.setCreatedTime(LocalDateTime.now().minusMinutes(35));
        order.setUpdatedTime(LocalDateTime.now().minusMinutes(35));
        order.setDeleted(0);
        return order;
    }
}
