#!/bin/bash

# 秒杀系统压力测试脚本

BASE_URL="http://localhost:8080"
PRODUCT_ID=1
CONCURRENT_USERS=100
TOTAL_REQUESTS=1000

echo "开始秒杀系统压力测试..."
echo "产品ID: $PRODUCT_ID"
echo "并发用户数: $CONCURRENT_USERS"
echo "总请求数: $TOTAL_REQUESTS"

# 初始化库存
echo "初始化商品库存..."
curl -X POST "$BASE_URL/seckill/initStock/$PRODUCT_ID"
echo -e "\n"

# 查看初始库存
echo "查看初始库存..."
curl -X GET "$BASE_URL/seckill/stock/$PRODUCT_ID"
echo -e "\n\n"

# 使用Apache Bench进行压力测试
echo "开始压力测试..."
ab -n $TOTAL_REQUESTS -c $CONCURRENT_USERS -p post_data.txt -T application/x-www-form-urlencoded "$BASE_URL/seckill/doSeckill"

# 查看最终库存
echo -e "\n\n测试完成，查看最终库存..."
curl -X GET "$BASE_URL/seckill/stock/$PRODUCT_ID"
echo -e "\n"