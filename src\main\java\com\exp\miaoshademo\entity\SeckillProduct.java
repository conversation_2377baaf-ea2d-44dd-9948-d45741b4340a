package com.exp.miaoshademo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("seckill_product")
public class SeckillProduct {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    private String productName;
    
    private BigDecimal originalPrice;
    
    private BigDecimal seckillPrice;
    
    private Integer totalStock;
    
    private Integer availableStock;
    
    private LocalDateTime startTime;
    
    private LocalDateTime endTime;
    
    private Integer status;
    
    @Version
    private Integer version;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
    
    @TableLogic
    private Integer deleted;
}