-- 限流Lua脚本
-- KEYS[1]: 限流key
-- ARGV[1]: 限流次数
-- ARGV[2]: 时间窗口（秒）

local key = KEYS[1]
local limit = tonumber(ARGV[1])
local window = tonumber(ARGV[2])

-- 获取当前访问次数
local current = redis.call('GET', key)

if current == false then
    -- 第一次访问，设置计数为1，设置过期时间
    redis.call('SET', key, 1)
    redis.call('EXPIRE', key, window)
    return {1, limit}
else
    current = tonumber(current)
    if current < limit then
        -- 访问次数未达到限制，递增计数
        local newCount = redis.call('INCR', key)
        return {newCount, limit}
    else
        -- 访问次数已达到限制
        return {current, limit}
    end
end