package com.exp.miaoshademo.service;

import com.exp.miaoshademo.config.RocketMQConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

@Service
@RocketMQMessageListener(
    topic = RocketMQConfig.STOCK_SYNC_TOPIC,
    consumerGroup = RocketMQConfig.STOCK_SYNC_GROUP,
    selectorExpression = RocketMQConfig.STOCK_SYNC_TAG
)
@RequiredArgsConstructor
@Slf4j
public class StockSyncMessageConsumer implements RocketMQListener<String> {

    private final ObjectMapper objectMapper;
    private final RedisStockService redisStockService;

    @Override
    public void onMessage(String message) {
        log.info("收到库存同步消息: {}", message);
        
        try {
            RocketMQProducerService.StockSyncMessage stockSyncMessage = 
                objectMapper.readValue(message, RocketMQProducerService.StockSyncMessage.class);
            
            redisStockService.syncStockFromDB(stockSyncMessage.getProductId(), stockSyncMessage.getStock());
            
            log.info("库存同步完成 - 商品:{}, 库存:{}", 
                stockSyncMessage.getProductId(), stockSyncMessage.getStock());
            
        } catch (Exception e) {
            log.error("处理库存同步消息失败: {}", message, e);
        }
    }
}