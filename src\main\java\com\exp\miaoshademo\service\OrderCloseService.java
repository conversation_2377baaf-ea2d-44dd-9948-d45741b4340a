package com.exp.miaoshademo.service;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单关闭服务管理类
 * 负责协调生产者和消费者的启动和监控
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "order.close", name = "enabled", havingValue = "true", matchIfMissing = true)
public class OrderCloseService {
    
    private final OrderCloseProducer producer;
    private final OrderCloseConsumer consumer;
    
    @PostConstruct
    public void init() {
        log.info("订单关闭服务已启动");
        log.info("生产者定时任务已配置");
        log.info("消费者线程池已启动");
    }
    
    /**
     * 获取订单关闭服务整体状态
     */
    public OrderCloseServiceStatus getServiceStatus() {
        OrderCloseProducer.ProducerStats producerStats = producer.getStats();
        OrderCloseConsumer.ConsumerStats consumerStats = consumer.getStats();
        
        return new OrderCloseServiceStatus(producerStats, consumerStats);
    }
    
    /**
     * 手动触发一次订单扫描
     */
    public void triggerManualScan() {
        log.info("手动触发订单扫描");
        producer.scanTimeoutOrders();
    }
    
    /**
     * 订单关闭服务状态
     */
    public static class OrderCloseServiceStatus {
        private final OrderCloseProducer.ProducerStats producerStats;
        private final OrderCloseConsumer.ConsumerStats consumerStats;
        
        public OrderCloseServiceStatus(OrderCloseProducer.ProducerStats producerStats, 
                                     OrderCloseConsumer.ConsumerStats consumerStats) {
            this.producerStats = producerStats;
            this.consumerStats = consumerStats;
        }
        
        public Map<String, Object> toMap() {
            Map<String, Object> status = new HashMap<>();
            
            // 生产者状态
            Map<String, Object> producer = new HashMap<>();
            producer.put("running", producerStats.isRunning());
            producer.put("totalProduced", producerStats.getTotalProduced());
            producer.put("queueSize", producerStats.getQueueSize());
            producer.put("lastScanTime", producerStats.getLastScanTime());
            status.put("producer", producer);
            
            // 消费者状态
            Map<String, Object> consumer = new HashMap<>();
            consumer.put("running", consumerStats.isRunning());
            consumer.put("totalConsumed", consumerStats.getTotalConsumed());
            consumer.put("totalSuccess", consumerStats.getTotalSuccess());
            consumer.put("totalFailed", consumerStats.getTotalFailed());
            consumer.put("successRate", String.format("%.2f%%", consumerStats.getSuccessRate() * 100));
            consumer.put("activeThreads", consumerStats.getActiveThreads());
            consumer.put("queueSize", consumerStats.getQueueSize());
            status.put("consumer", consumer);
            
            // 整体状态
            status.put("healthy", producerStats.isRunning() && consumerStats.isRunning());
            status.put("timestamp", System.currentTimeMillis());
            
            return status;
        }
        
        // Getters
        public OrderCloseProducer.ProducerStats getProducerStats() { return producerStats; }
        public OrderCloseConsumer.ConsumerStats getConsumerStats() { return consumerStats; }
    }
}
