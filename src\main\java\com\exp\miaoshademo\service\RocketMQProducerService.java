package com.exp.miaoshademo.service;

import com.exp.miaoshademo.config.RocketMQConfig;
import com.exp.miaoshademo.dto.SeckillMessage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.LocalTransactionState;
import org.apache.rocketmq.client.producer.TransactionSendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class RocketMQProducerService {

    private final RocketMQTemplate rocketMQTemplate;
    private final ObjectMapper objectMapper;

    public String sendSeckillTransactionMessage(SeckillMessage seckillMessage) {
        try {
            String messageBody = objectMapper.writeValueAsString(seckillMessage);
            String destination = RocketMQConfig.SECKILL_TOPIC + ":" + RocketMQConfig.SECKILL_TAG;
            
            Message<String> message = MessageBuilder
                .withPayload(messageBody)
                .setHeader("orderNo", seckillMessage.getOrderNo())
                .setHeader("userId", seckillMessage.getUserId())
                .setHeader("productId", seckillMessage.getProductId())
                .build();
            
            TransactionSendResult sendResult = rocketMQTemplate.sendMessageInTransaction(destination, message, seckillMessage);
            log.info("发送秒杀事务消息 - 订单号:{}, 用户:{}, 商品:{}, 状态:{}", 
                seckillMessage.getOrderNo(), seckillMessage.getUserId(), seckillMessage.getProductId(), 
                sendResult.getLocalTransactionState());
            
            if (sendResult.getLocalTransactionState() == LocalTransactionState.COMMIT_MESSAGE) {
                return "SUCCESS";
            } else if (sendResult.getLocalTransactionState() == LocalTransactionState.ROLLBACK_MESSAGE) {
                return "库存不足或系统异常";
            } else {
                return "系统异常，事务状态未知";
            }
        } catch (JsonProcessingException e) {
            log.error("序列化秒杀消息失败", e);
            return "系统异常";
        } catch (Exception e) {
            log.error("发送秒杀事务消息失败", e);
            return "系统异常";
        }
    }

    public void sendStockSyncMessage(Long productId, Integer stock) {
        try {
            String messageBody = objectMapper.writeValueAsString(new StockSyncMessage(productId, stock));
            String destination = RocketMQConfig.STOCK_SYNC_TOPIC + ":" + RocketMQConfig.STOCK_SYNC_TAG;
            
            rocketMQTemplate.convertAndSend(destination, messageBody);
            log.info("发送库存同步消息成功 - 商品:{}, 库存:{}", productId, stock);
        } catch (JsonProcessingException e) {
            log.error("序列化库存同步消息失败", e);
            throw new RuntimeException("发送库存同步消息失败", e);
        }
    }

    public static class StockSyncMessage {
        private Long productId;
        private Integer stock;
        private Long timestamp;

        public StockSyncMessage(Long productId, Integer stock) {
            this.productId = productId;
            this.stock = stock;
            this.timestamp = System.currentTimeMillis();
        }

        public Long getProductId() {
            return productId;
        }

        public Integer getStock() {
            return stock;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setProductId(Long productId) {
            this.productId = productId;
        }

        public void setStock(Integer stock) {
            this.stock = stock;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }
    }
}