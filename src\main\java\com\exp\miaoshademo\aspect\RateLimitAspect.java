package com.exp.miaoshademo.aspect;

import com.exp.miaoshademo.annotation.RateLimit;
import com.exp.miaoshademo.service.RateLimitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Aspect
@Component
@Order(1)
@RequiredArgsConstructor
@Slf4j
public class RateLimitAspect {

    private final RateLimitService rateLimitService;

    @Around("@annotation(rateLimit)")
    public Object around(ProceedingJoinPoint point, RateLimit rateLimit) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        
        String key = rateLimit.key();
        if (key.isEmpty()) {
            key = request.getRequestURI() + ":" + getClientIpAddress(request);
        }
        
        boolean allowed = rateLimitService.isAllowed(key, rateLimit.count(), rateLimit.time());
        
        if (!allowed) {
            log.warn("限流触发 - key:{}, 限制:{}/{}秒", key, rateLimit.count(), rateLimit.time());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", rateLimit.message());
            return result;
        }
        
        return point.proceed();
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (xForwardedForHeader == null || xForwardedForHeader.isEmpty() || "unknown".equalsIgnoreCase(xForwardedForHeader)) {
            String xRealIpHeader = request.getHeader("X-Real-IP");
            if (xRealIpHeader == null || xRealIpHeader.isEmpty() || "unknown".equalsIgnoreCase(xRealIpHeader)) {
                return request.getRemoteAddr();
            } else {
                return xRealIpHeader;
            }
        } else {
            return xForwardedForHeader.split(",")[0].trim();
        }
    }
}