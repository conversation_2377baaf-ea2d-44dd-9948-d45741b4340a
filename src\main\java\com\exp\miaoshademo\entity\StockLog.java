package com.exp.miaoshademo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("stock_log")
public class StockLog {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    private Long productId;
    
    private Long userId;
    
    private String orderNo;
    
    private Integer changeAmount;
    
    private Integer beforeStock;
    
    private Integer afterStock;
    
    private Integer operationType;
    
    private Integer status;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
}