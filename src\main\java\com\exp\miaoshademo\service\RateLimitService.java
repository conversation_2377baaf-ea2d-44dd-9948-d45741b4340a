package com.exp.miaoshademo.service;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class RateLimitService {

    private final RedisTemplate<String, Object> redisTemplate;
    private DefaultRedisScript<List> rateLimitScript;
    
    private static final String RATE_LIMIT_KEY_PREFIX = "rate_limit:";

    @PostConstruct
    public void init() {
        rateLimitScript = new DefaultRedisScript<>();
        rateLimitScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("lua/rate_limit.lua")));
        rateLimitScript.setResultType(List.class);
    }

    public boolean isAllowed(String key, int limit, int window) {
        String rateLimitKey = RATE_LIMIT_KEY_PREFIX + key;
        
        try {
            List result = redisTemplate.execute(
                rateLimitScript,
                Collections.singletonList(rateLimitKey),
                String.valueOf(limit),
                String.valueOf(window)
            );
            
            if (result != null && result.size() == 2) {
                int current = Integer.parseInt(result.get(0).toString());
                int maxLimit = Integer.parseInt(result.get(1).toString());
                
                log.debug("限流检查 - key:{}, 当前次数:{}, 最大限制:{}", key, current, maxLimit);
                
                return current <= maxLimit;
            }
            
            return false;
        } catch (Exception e) {
            log.error("限流检查失败 - key:{}", key, e);
            return true;
        }
    }

    public void reset(String key) {
        String rateLimitKey = RATE_LIMIT_KEY_PREFIX + key;
        redisTemplate.delete(rateLimitKey);
        log.info("重置限流计数器 - key:{}", key);
    }
}