package com.exp.miaoshademo.service;

import com.exp.miaoshademo.dto.SeckillMessage;
import com.exp.miaoshademo.entity.SeckillOrder;
import com.exp.miaoshademo.entity.StockLog;
import com.exp.miaoshademo.entity.UserSeckillRecord;
import com.exp.miaoshademo.mapper.SeckillOrderMapper;
import com.exp.miaoshademo.mapper.StockLogMapper;
import com.exp.miaoshademo.mapper.UserSeckillRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQTransactionListener;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionListener;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionState;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Component
@RocketMQTransactionListener
@RequiredArgsConstructor
@Slf4j
public class SeckillTransactionListener implements RocketMQLocalTransactionListener {

    private final SeckillOrderMapper orderMapper;
    private final StockLogMapper stockLogMapper;
    private final UserSeckillRecordMapper userSeckillRecordMapper;
    private final DatabaseStockService databaseStockService;
    private final RedisStockService redisStockService;

    @Override
    public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
        SeckillMessage seckillMessage = (SeckillMessage) arg;
        log.info("执行本地事务（Redis扣减） - 订单号:{}", seckillMessage.getOrderNo());
        
        try {
            RedisStockService.SeckillResult redisResult = redisStockService.seckillStock(
                seckillMessage.getUserId(), 
                seckillMessage.getProductId(), 
                seckillMessage.getQuantity()
            );
            
            if (!redisResult.isSuccess()) {
                log.warn("Redis库存扣减失败 - 订单号:{}, 原因:{}", 
                    seckillMessage.getOrderNo(), redisResult.getMessage());
                return RocketMQLocalTransactionState.ROLLBACK;
            }
            
            seckillMessage.setBeforeStock(redisResult.getRemainStock() + seckillMessage.getQuantity());
            seckillMessage.setAfterStock(redisResult.getRemainStock());
            
            log.info("Redis库存扣减成功 - 订单号:{}, 剩余库存:{}", 
                seckillMessage.getOrderNo(), redisResult.getRemainStock());
            
            return RocketMQLocalTransactionState.COMMIT;
            
        } catch (Exception e) {
            log.error("本地事务执行失败 - 订单号:{}", seckillMessage.getOrderNo(), e);
            return RocketMQLocalTransactionState.ROLLBACK;
        }
    }

    @Override
    public RocketMQLocalTransactionState checkLocalTransaction(Message msg) {
        String orderNo = (String) msg.getHeaders().get("orderNo");
        Long userId = Long.valueOf(msg.getHeaders().get("userId").toString());
        Long productId = Long.valueOf(msg.getHeaders().get("productId").toString());
        
        log.info("检查本地事务状态（Redis扣减状态） - 订单号:{}", orderNo);
        
        try {
            String userKey = "seckill:user:" + userId + ":" + productId;
            Object userRecord = redisStockService.redisTemplate.opsForValue().get(userKey);
            
            if (userRecord != null) {
                log.info("Redis扣减记录存在，事务已提交 - 订单号:{}", orderNo);
                return RocketMQLocalTransactionState.COMMIT;
            } else {
                log.info("Redis扣减记录不存在，事务需回滚 - 订单号:{}", orderNo);
                return RocketMQLocalTransactionState.ROLLBACK;
            }
        } catch (Exception e) {
            log.error("检查本地事务状态失败 - 订单号:{}", orderNo, e);
            return RocketMQLocalTransactionState.ROLLBACK;
        }
    }
}