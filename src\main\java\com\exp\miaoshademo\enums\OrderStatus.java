package com.exp.miaoshademo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum OrderStatus {
    
    PENDING_PAYMENT(0, "待支付"),
    PAID(1, "已支付"),
    CANCELLED(2, "已取消"),
    REFUNDED(3, "已退款");
    
    private final Integer code;
    private final String description;
    
    /**
     * 根据状态码获取枚举
     */
    public static OrderStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为待支付状态
     */
    public static boolean isPendingPayment(Integer status) {
        return PENDING_PAYMENT.getCode().equals(status);
    }
    
    /**
     * 判断是否可以关闭订单
     */
    public static boolean canClose(Integer status) {
        return PENDING_PAYMENT.getCode().equals(status);
    }
}
