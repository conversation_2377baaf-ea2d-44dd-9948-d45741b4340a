-- 秒杀库存扣减Lua脚本
-- KEYS[1]: 商品库存key
-- KEYS[2]: 用户购买记录key
-- ARGV[1]: 用户ID
-- ARGV[2]: 商品ID
-- ARGV[3]: 扣减数量
-- ARGV[4]: 过期时间（秒）

local stockKey = KEYS[1]
local userKey = KEYS[2]
local userId = ARGV[1]
local productId = ARGV[2]
local quantity = tonumber(ARGV[3])
local expireTime = tonumber(ARGV[4])

-- 检查用户是否已经购买过
local userBought = redis.call('EXISTS', userKey)
if userBought == 1 then
    return {-1, "用户已购买过该商品"}
end

-- 获取当前库存
local currentStock = redis.call('GET', stockKey)
if not currentStock then
    return {-2, "商品不存在"}
end

currentStock = tonumber(currentStock)
if currentStock < quantity then
    return {-3, "库存不足"}
end

-- 扣减库存
local newStock = currentStock - quantity
redis.call('SET', stockKey, newStock)

-- 记录用户购买记录，设置过期时间
redis.call('SETEX', userKey, expireTime, userId .. ':' .. productId)

-- 返回成功结果
return {1, "库存扣减成功", newStock}