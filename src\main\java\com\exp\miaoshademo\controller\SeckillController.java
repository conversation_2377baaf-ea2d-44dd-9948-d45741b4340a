package com.exp.miaoshademo.controller;

import com.exp.miaoshademo.annotation.RateLimit;
import com.exp.miaoshademo.entity.SeckillProduct;
import com.exp.miaoshademo.service.SeckillService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/seckill")
@RequiredArgsConstructor
@Slf4j
public class SeckillController {

    private final SeckillService seckillService;

    @PostMapping("/doSeckill")
    @RateLimit(count = 5, time = 10, message = "秒杀过于频繁，请稍后再试")
    public Map<String, Object> doSeckill(@RequestParam Long userId,
                                         @RequestParam Long productId,
                                         @RequestParam(defaultValue = "1") Integer quantity) {
        log.info("收到秒杀请求 - 用户:{}, 商品:{}, 数量:{}", userId, productId, quantity);
        
        SeckillService.SeckillResult result = seckillService.doSeckill(userId, productId, quantity);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", result.isSuccess());
        response.put("message", result.getMessage());
        
        if (result.isSuccess()) {
            response.put("orderNo", result.getOrderNo());
            response.put("remainStock", result.getRemainStock());
        }
        
        return response;
    }

    @GetMapping("/product/{productId}")
    public Map<String, Object> getProduct(@PathVariable Long productId) {
        SeckillProduct product = seckillService.getProduct(productId);
        Integer redisStock = seckillService.getRedisStock(productId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("product", product);
        response.put("redisStock", redisStock);
        
        return response;
    }

    @PostMapping("/initStock/{productId}")
    public Map<String, Object> initStock(@PathVariable Long productId) {
        seckillService.initProductStock(productId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "库存初始化成功");
        
        return response;
    }

    @GetMapping("/stock/{productId}")
    public Map<String, Object> getStock(@PathVariable Long productId) {
        Integer redisStock = seckillService.getRedisStock(productId);
        SeckillProduct product = seckillService.getProduct(productId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("redisStock", redisStock);
        response.put("dbStock", product != null ? product.getAvailableStock() : 0);
        
        return response;
    }
}