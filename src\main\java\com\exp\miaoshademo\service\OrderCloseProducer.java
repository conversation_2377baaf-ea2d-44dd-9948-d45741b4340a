package com.exp.miaoshademo.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exp.miaoshademo.config.OrderCloseConfig;
import com.exp.miaoshademo.dto.OrderCloseTask;
import com.exp.miaoshademo.entity.SeckillOrder;
import com.exp.miaoshademo.enums.OrderStatus;
import com.exp.miaoshademo.mapper.SeckillOrderMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单关闭生产者服务
 * 负责定时扫描数据库，查询超时订单并放入阻塞队列
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderCloseProducer {
    
    private final SeckillOrderMapper orderMapper;
    private final OrderCloseConfig config;
    private final BlockingQueue<OrderCloseTask> orderCloseQueue;
    
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong totalProduced = new AtomicLong(0);
    private final AtomicLong lastScanTime = new AtomicLong(0);
    
    /**
     * 定时扫描超时订单
     */
    @Scheduled(fixedDelayString = "#{@orderCloseConfig.scheduleIntervalSeconds * 1000}")
    public void scanTimeoutOrders() {
        if (!config.isEnabled()) {
            return;
        }
        
        if (!isRunning.compareAndSet(false, true)) {
            log.warn("订单关闭生产者正在运行中，跳过本次扫描");
            return;
        }
        
        try {
            long startTime = System.currentTimeMillis();
            lastScanTime.set(startTime);
            
            log.info("开始扫描超时订单，超时时间: {} 分钟", config.getTimeoutMinutes());
            
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(config.getTimeoutMinutes());
            int totalProcessed = 0;
            int currentPage = 1;
            
            while (true) {
                // 分页查询超时订单
                IPage<SeckillOrder> page = new Page<>(currentPage, config.getPageSize());
                LambdaQueryWrapper<SeckillOrder> queryWrapper = new LambdaQueryWrapper<SeckillOrder>()
                        .eq(SeckillOrder::getStatus, OrderStatus.PENDING_PAYMENT.getCode())
                        .le(SeckillOrder::getCreatedTime, timeoutThreshold)
                        .eq(SeckillOrder::getDeleted, 0)
                        .orderByAsc(SeckillOrder::getCreatedTime);
                
                IPage<SeckillOrder> orderPage = orderMapper.selectPage(page, queryWrapper);
                List<SeckillOrder> orders = orderPage.getRecords();
                
                if (orders.isEmpty()) {
                    break;
                }
                
                log.info("第 {} 页查询到 {} 个超时订单", currentPage, orders.size());
                
                // 将订单转换为任务并放入队列
                int batchProcessed = processOrderBatch(orders);
                totalProcessed += batchProcessed;
                
                // 如果是最后一页，退出循环
                if (currentPage >= orderPage.getPages()) {
                    break;
                }
                
                currentPage++;
                
                // 批次间隔，避免对数据库造成过大压力
                if (config.getProducerBatchIntervalMs() > 0) {
                    Thread.sleep(config.getProducerBatchIntervalMs());
                }
            }
            
            long endTime = System.currentTimeMillis();
            totalProduced.addAndGet(totalProcessed);
            
            log.info("订单扫描完成，本次处理 {} 个订单，耗时 {} ms，队列当前大小: {}", 
                    totalProcessed, endTime - startTime, orderCloseQueue.size());
            
        } catch (Exception e) {
            log.error("扫描超时订单时发生异常", e);
        } finally {
            isRunning.set(false);
        }
    }
    
    /**
     * 批量处理订单
     */
    private int processOrderBatch(List<SeckillOrder> orders) {
        int processed = 0;
        
        for (SeckillOrder order : orders) {
            try {
                OrderCloseTask task = convertToTask(order);
                
                // 尝试将任务放入队列
                if (orderCloseQueue.offer(task)) {
                    processed++;
                    log.debug("订单关闭任务已放入队列: {}", order.getOrderNo());
                } else {
                    log.warn("队列已满，无法放入订单关闭任务: {}", order.getOrderNo());
                    break; // 队列满了，停止处理
                }
                
            } catch (Exception e) {
                log.error("处理订单 {} 时发生异常", order.getOrderNo(), e);
            }
        }
        
        return processed;
    }
    
    /**
     * 将订单转换为关闭任务
     */
    private OrderCloseTask convertToTask(SeckillOrder order) {
        return new OrderCloseTask(
                order.getId(),
                order.getOrderNo(),
                order.getUserId(),
                order.getProductId(),
                order.getProductName(),
                order.getQuantity(),
                order.getSeckillPrice(),
                order.getTotalAmount(),
                order.getStatus(),
                order.getCreatedTime()
        );
    }
    
    /**
     * 获取生产者统计信息
     */
    public ProducerStats getStats() {
        return new ProducerStats(
                isRunning.get(),
                totalProduced.get(),
                orderCloseQueue.size(),
                lastScanTime.get()
        );
    }
    
    /**
     * 生产者统计信息
     */
    public static class ProducerStats {
        private final boolean running;
        private final long totalProduced;
        private final int queueSize;
        private final long lastScanTime;
        
        public ProducerStats(boolean running, long totalProduced, int queueSize, long lastScanTime) {
            this.running = running;
            this.totalProduced = totalProduced;
            this.queueSize = queueSize;
            this.lastScanTime = lastScanTime;
        }
        
        // Getters
        public boolean isRunning() { return running; }
        public long getTotalProduced() { return totalProduced; }
        public int getQueueSize() { return queueSize; }
        public long getLastScanTime() { return lastScanTime; }
    }
}
