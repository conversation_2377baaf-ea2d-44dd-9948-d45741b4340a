package com.exp.miaoshademo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.exp.miaoshademo.entity.SeckillProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface SeckillProductMapper extends BaseMapper<SeckillProduct> {

    /**
     * 扣减库存
     * @param productId: 商品ID
     * @param quantity: 扣减数量
     * @param version: 版本号
     * @return 扣减结果
     */
    @Update("UPDATE seckill_product SET available_stock = available_stock - #{quantity}, version = version + 1 " +
            "WHERE id = #{productId} AND available_stock >= #{quantity} AND version = #{version}")
    int deductStock(@Param("productId") Long productId, @Param("quantity") Integer quantity, @Param("version") Integer version);

    @Update("UPDATE seckill_product SET available_stock = available_stock - #{quantity} " +
            "WHERE id = #{productId} AND available_stock >= #{quantity}")
    int deductStockWithoutVersion(@Param("productId") Long productId, @Param("quantity") Integer quantity);

    /**
     * 恢复库存（订单取消时使用）
     * @param productId: 商品ID
     * @param quantity: 恢复数量
     * @param version: 版本号
     * @return 恢复结果
     */
    @Update("UPDATE seckill_product SET available_stock = available_stock + #{quantity}, version = version + 1 " +
            "WHERE id = #{productId} AND version = #{version}")
    int restoreStock(@Param("productId") Long productId, @Param("quantity") Integer quantity, @Param("version") Integer version);
}