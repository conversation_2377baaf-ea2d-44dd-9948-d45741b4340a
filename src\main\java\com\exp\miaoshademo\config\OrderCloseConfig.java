package com.exp.miaoshademo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 订单关闭配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "order.close")
public class OrderCloseConfig {
    
    /**
     * 是否启用订单关闭功能
     */
    private boolean enabled = true;
    
    /**
     * 订单超时时间（分钟）
     */
    private int timeoutMinutes = 30;
    
    /**
     * 定时任务执行间隔（秒）
     */
    private int scheduleIntervalSeconds = 60;
    
    /**
     * 分页查询大小
     */
    private int pageSize = 100;
    
    /**
     * 阻塞队列容量
     */
    private int queueCapacity = 1000;
    
    /**
     * 消费者线程池核心线程数
     */
    private int corePoolSize = 5;
    
    /**
     * 消费者线程池最大线程数
     */
    private int maxPoolSize = 10;
    
    /**
     * 线程池队列容量
     */
    private int threadPoolQueueCapacity = 200;
    
    /**
     * 线程空闲时间（秒）
     */
    private int keepAliveSeconds = 60;
    
    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;
    
    /**
     * 批量处理大小
     */
    private int batchSize = 50;
    
    /**
     * 消费者等待时间（毫秒）
     */
    private long consumerWaitTimeMs = 1000;
    
    /**
     * 生产者批次间隔时间（毫秒）
     */
    private long producerBatchIntervalMs = 100;
}
