package com.exp.miaoshademo.service;

import com.exp.miaoshademo.entity.SeckillProduct;
import com.exp.miaoshademo.mapper.SeckillProductMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class DatabaseStockService {

    private final SeckillProductMapper productMapper;

    public boolean deductStock(Long productId, Integer quantity, Integer beforeStock) {
        try {
            SeckillProduct product = productMapper.selectById(productId);
            if (product == null) {
                log.error("商品不存在 - 商品ID:{}", productId);
                return false;
            }
            
            if (product.getAvailableStock() < quantity) {
                log.error("库存不足 - 商品ID:{}, 当前库存:{}, 需要扣减:{}", 
                    productId, product.getAvailableStock(), quantity);
                return false;
            }
            
            int updated = productMapper.deductStock(productId, quantity, product.getVersion());
            if (updated > 0) {
                log.info("数据库库存扣减成功 - 商品ID:{}, 扣减数量:{}, 版本:{}", 
                    productId, quantity, product.getVersion());
                return true;
            } else {
                log.warn("数据库库存扣减失败，可能是并发冲突 - 商品ID:{}, 扣减数量:{}, 版本:{}", 
                    productId, quantity, product.getVersion());
                return false;
            }
        } catch (Exception e) {
            log.error("数据库库存扣减异常 - 商品ID:{}, 扣减数量:{}", productId, quantity, e);
            return false;
        }
    }

    public SeckillProduct getProduct(Long productId) {
        return productMapper.selectById(productId);
    }

    public boolean updateStock(Long productId, Integer stock) {
        try {
            SeckillProduct product = productMapper.selectById(productId);
            if (product == null) {
                return false;
            }
            
            product.setAvailableStock(stock);
            int updated = productMapper.updateById(product);
            return updated > 0;
        } catch (Exception e) {
            log.error("更新数据库库存失败 - 商品ID:{}, 库存:{}", productId, stock, e);
            return false;
        }
    }
}