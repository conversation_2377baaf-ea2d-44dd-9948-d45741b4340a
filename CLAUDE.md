# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a high-performance seckill (flash sale) system built with Spring Boot that demonstrates how to handle extreme concurrency while preventing overselling. The system uses Redis + Lua scripts for atomic inventory deduction and RocketMQ transactional messages to ensure eventual consistency between Redis and MySQL.

## Core Architecture

The system implements a **RocketMQ transactional message pattern** where:

1. **Half Message**: Send a RocketMQ transaction message (half message state)
2. **Local Transaction**: Execute Redis stock deduction using Lua script atomically
3. **Message Decision**: Commit message if Redis deduction succeeds, rollback if fails
4. **Consumer Processing**: Only committed messages are consumed to perform MySQL operations

**Critical Flow**: Redis serves as the fast pre-deduction layer, MySQL ensures data persistence, and RocketMQ transactional messages guarantee consistency between them.

## Key Components

### Redis Layer (`RedisStockService` + Lua Scripts)
- **Purpose**: Atomic inventory deduction and duplicate purchase prevention
- **Script Location**: `src/main/resources/lua/seckill_stock.lua`
- **Key Operations**: Stock check, deduction, and user record creation in one atomic operation

### Transaction Message (`SeckillTransactionListener`)
- **executeLocalTransaction()**: Performs Redis deduction as local transaction
- **checkLocalTransaction()**: Verifies Redis state for message recovery
- **Consumer**: `SeckillMessageConsumer` handles MySQL operations (orders, stock, logs)

### Rate Limiting (`@RateLimit` + `RateLimitAspect`)
- **Implementation**: Redis-based sliding window using Lua script
- **Scope**: IP-level and endpoint-level rate limiting
- **Script**: `src/main/resources/lua/rate_limit.lua`

## Development Commands

### Build and Run
```bash
# Build the project
mvn clean compile

# Run the application
mvn spring-boot:run

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### Testing
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=SeckillConcurrentTest

# Run concurrent stress test
mvn test -Dtest=SeckillConcurrentTest#testConcurrentSeckill
```

### Database Setup
```bash
# Initialize database (execute SQL file)
mysql -u root -p < src/main/resources/seckill_db.sql

# Or run the SQL script directly in MySQL
source src/main/resources/seckill_db.sql;
```

### Performance Testing
```bash
# Apache Bench stress test
chmod +x stress_test.sh
./stress_test.sh

# JMeter test plan
jmeter -n -t jmeter_test_plan.jmx -l results.jtl
```

## External Dependencies Setup

Before running the application, ensure these services are running:

### Redis
```bash
redis-server
# Default: localhost:6379
```

### RocketMQ
```bash
# Start NameServer
nohup sh mqnamesrv &

# Start Broker
nohup sh mqbroker -n localhost:9876 &
```

### MySQL
- Database: `seckill_db`
- Default credentials: root/123456 (change in `application.properties`)

## Configuration

### Key Configuration Files
- **application.properties**: Main configuration (DB, Redis, RocketMQ)
- **RedisConfig.java**: Redis template and Lua script bean configuration
- **RocketMQConfig.java**: Topic and consumer group constants

### Important Configuration Notes
- **Database URL**: Must include `allowPublicKeyRetrieval=true` for MySQL 8.0+
- **Redis Pool**: Configured for high concurrency (max-active=10, max-idle=10)
- **RocketMQ**: Producer retry configuration for reliability

## Testing Strategy

### Unit Tests
- Focus on business logic in service classes
- Mock external dependencies (Redis, RocketMQ, Database)

### Integration Tests  
- **SeckillConcurrentTest**: Tests actual concurrency scenarios
- Verifies that multiple concurrent requests don't cause overselling
- Tests duplicate purchase prevention

### Performance Tests
- **Stress Test Script**: Tests system under load
- **JMeter Plan**: Configurable concurrent user simulation
- **Metrics**: Success rate, response time, error rate

## Monitoring and Debugging

### Key Logs to Monitor
- Redis stock deduction results in `RedisStockService`
- Transaction message states in `SeckillTransactionListener`
- Consumer processing status in `SeckillMessageConsumer`
- Rate limiting triggers in `RateLimitAspect`

### Common Issues
- **Redis Connection**: Check if Redis is running and accessible
- **RocketMQ Connectivity**: Verify NameServer and Broker are running
- **Database Locks**: Monitor for deadlocks in high concurrency scenarios
- **Message Accumulation**: Check RocketMQ consumer lag

## Code Modification Guidelines

### Adding New Seckill Logic
1. Business validation should be in `SeckillService.doSeckill()`
2. Redis operations must use Lua scripts for atomicity
3. Database operations should be handled in message consumers
4. Always add appropriate rate limiting

### Extending Rate Limiting
1. Add new `@RateLimit` annotations to controllers
2. Modify Lua script in `rate_limit.lua` for different algorithms  
3. Configure limits in `RateLimitService`

### Database Schema Changes
1. Update entity classes in `entity/` package
2. Modify corresponding mapper interfaces
3. Update `seckill_db.sql` for new installations
4. Consider migration scripts for existing databases

## Performance Considerations

- **Lua Scripts**: Keep them lightweight, avoid complex logic
- **Database Queries**: Use optimistic locking (version field) for concurrent updates
- **Connection Pooling**: Tune Redis and DB connection pools for expected load
- **Message Processing**: Monitor consumer lag and scale consumers accordingly