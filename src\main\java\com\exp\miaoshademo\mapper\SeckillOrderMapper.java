package com.exp.miaoshademo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.exp.miaoshademo.entity.SeckillOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SeckillOrderMapper extends BaseMapper<SeckillOrder> {
    
    @Select("SELECT * FROM seckill_order WHERE order_no = #{orderNo} AND deleted = 0")
    SeckillOrder selectByOrderNo(@Param("orderNo") String orderNo);
    
    @Select("SELECT COUNT(*) FROM seckill_order WHERE user_id = #{userId} AND product_id = #{productId} AND deleted = 0")
    int countByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);
}