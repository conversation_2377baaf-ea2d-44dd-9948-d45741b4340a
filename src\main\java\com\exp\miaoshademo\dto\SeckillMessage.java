package com.exp.miaoshademo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SeckillMessage implements Serializable {
    
    private Long userId;
    private Long productId;
    private String orderNo;
    private Integer quantity;
    private BigDecimal seckillPrice;
    private String productName;
    private Integer beforeStock;
    private Integer afterStock;
    private Long timestamp;
    
    public SeckillMessage(Long userId, Long productId, String orderNo, Integer quantity, 
                         BigDecimal seckillPrice, String productName) {
        this.userId = userId;
        this.productId = productId;
        this.orderNo = orderNo;
        this.quantity = quantity;
        this.seckillPrice = seckillPrice;
        this.productName = productName;
        this.timestamp = System.currentTimeMillis();
    }
}