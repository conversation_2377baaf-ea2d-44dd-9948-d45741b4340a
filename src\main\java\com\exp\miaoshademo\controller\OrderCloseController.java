package com.exp.miaoshademo.controller;

import com.exp.miaoshademo.service.OrderCloseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单关闭监控控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/order-close")
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "order.close", name = "enabled", havingValue = "true", matchIfMissing = true)
public class OrderCloseController {
    
    private final OrderCloseService orderCloseService;
    
    /**
     * 获取订单关闭服务状态
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        try {
            OrderCloseService.OrderCloseServiceStatus status = orderCloseService.getServiceStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", status.toMap());
            return result;
        } catch (Exception e) {
            log.error("获取订单关闭服务状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取状态失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 手动触发订单扫描
     */
    @PostMapping("/trigger-scan")
    public Map<String, Object> triggerScan() {
        try {
            orderCloseService.triggerManualScan();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "手动扫描已触发");
            return result;
        } catch (Exception e) {
            log.error("手动触发订单扫描失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "触发扫描失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        try {
            OrderCloseService.OrderCloseServiceStatus status = orderCloseService.getServiceStatus();
            Map<String, Object> result = new HashMap<>();
            
            boolean healthy = status.getProducerStats().isRunning() && 
                            status.getConsumerStats().isRunning();
            
            result.put("status", healthy ? "UP" : "DOWN");
            result.put("producer", status.getProducerStats().isRunning() ? "UP" : "DOWN");
            result.put("consumer", status.getConsumerStats().isRunning() ? "UP" : "DOWN");
            result.put("timestamp", System.currentTimeMillis());
            
            return result;
        } catch (Exception e) {
            log.error("健康检查失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("status", "DOWN");
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            return result;
        }
    }
}
