-- 创建秒杀数据库
CREATE DATABASE IF NOT EXISTS seckill_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE seckill_db;

-- 秒杀商品表
CREATE TABLE IF NOT EXISTS seckill_product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_name VARCHAR(255) NOT NULL COMMENT '商品名称',
    original_price DECIMAL(10,2) NOT NULL COMMENT '原价',
    seckill_price DECIMAL(10,2) NOT NULL COMMENT '秒杀价',
    total_stock INT NOT NULL COMMENT '总库存',
    available_stock INT NOT NULL COMMENT '可用库存',
    start_time DATETIME NOT NULL COMMENT '秒杀开始时间',
    end_time DATETIME NOT NULL COMMENT '秒杀结束时间',
    status TINYINT DEFAULT 0 COMMENT '状态：0-未开始 1-进行中 2-已结束',
    version INT DEFAULT 0 COMMENT '乐观锁版本号',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除 1-已删除',
    INDEX idx_status_time (status, start_time, end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀商品表';

-- 秒杀订单表
CREATE TABLE IF NOT EXISTS seckill_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(64) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(255) NOT NULL COMMENT '商品名称',
    seckill_price DECIMAL(10,2) NOT NULL COMMENT '秒杀价',
    quantity INT DEFAULT 1 COMMENT '购买数量',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    status TINYINT DEFAULT 0 COMMENT '订单状态：0-待支付 1-已支付 2-已取消 3-已退款',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除 1-已删除',
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀订单表';

-- 库存流水表（用于记录库存变化）
CREATE TABLE IF NOT EXISTS stock_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL COMMENT '商品ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    order_no VARCHAR(64) COMMENT '订单号',
    change_amount INT NOT NULL COMMENT '变化数量（负数表示扣减）',
    before_stock INT NOT NULL COMMENT '变化前库存',
    after_stock INT NOT NULL COMMENT '变化后库存',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-秒杀扣减 2-取消返还 3-补库存',
    status TINYINT DEFAULT 0 COMMENT '状态：0-处理中 1-成功 2-失败',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_product_id (product_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存流水表';

-- 用户秒杀记录表（防止重复秒杀）
CREATE TABLE IF NOT EXISTS user_seckill_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    order_no VARCHAR(64) NOT NULL COMMENT '订单号',
    seckill_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '秒杀时间',
    status TINYINT DEFAULT 1 COMMENT '状态：1-有效 0-无效',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_product (user_id, product_id) COMMENT '用户商品唯一约束',
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户秒杀记录表';

-- 初始化测试数据
INSERT INTO seckill_product (product_name, original_price, seckill_price, total_stock, available_stock, start_time, end_time, status) VALUES
('iPhone 15 Pro 512GB', 9999.00, 6999.00, 100, 100, '2025-01-01 10:00:00', '2025-12-31 23:59:59', 1),
('MacBook Pro 16寸', 19999.00, 15999.00, 50, 50, '2025-01-01 10:00:00', '2025-12-31 23:59:59', 1),
('小米14 Ultra 16GB+1TB', 6999.00, 4999.00, 200, 200, '2025-01-01 10:00:00', '2025-12-31 23:59:59', 1);