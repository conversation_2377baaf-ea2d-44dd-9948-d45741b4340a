package com.exp.miaoshademo.service;

import com.exp.miaoshademo.dto.SeckillMessage;
import com.exp.miaoshademo.entity.SeckillProduct;
import com.exp.miaoshademo.mapper.SeckillOrderMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

@Service
@RequiredArgsConstructor
@Slf4j
public class SeckillService {

    private final RedisStockService redisStockService;
    private final DatabaseStockService databaseStockService;
    private final RocketMQProducerService mqProducerService;
    private final SeckillOrderMapper orderMapper;
    
    private final AtomicLong orderCounter = new AtomicLong(0);

    public SeckillResult doSeckill(Long userId, Long productId, Integer quantity) {
        log.info("开始秒杀 - 用户:{}, 商品:{}, 数量:{}", userId, productId, quantity);
        
        try {
            SeckillProduct product = databaseStockService.getProduct(productId);
            if (product == null) {
                return SeckillResult.fail("商品不存在");
            }
            
            if (product.getStatus() != 1) {
                return SeckillResult.fail("秒杀未开始或已结束");
            }
            
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(product.getStartTime()) || now.isAfter(product.getEndTime())) {
                return SeckillResult.fail("不在秒杀时间范围内");
            }
            
            if (orderMapper.countByUserIdAndProductId(userId, productId) > 0) {
                return SeckillResult.fail("用户已购买过该商品");
            }
            
            String orderNo = generateOrderNo(userId, productId);
            
            SeckillMessage seckillMessage = new SeckillMessage(
                userId, productId, orderNo, quantity, 
                product.getSeckillPrice(), product.getProductName()
            );
            
            log.info("发送事务消息 - 用户:{}, 商品:{}, 订单号:{}", userId, productId, orderNo);
            String result = mqProducerService.sendSeckillTransactionMessage(seckillMessage);
            
            if ("SUCCESS".equals(result)) {
                log.info("秒杀成功 - 用户:{}, 商品:{}, 订单号:{}", userId, productId, orderNo);
                return SeckillResult.success(orderNo, null);
            } else {
                log.warn("秒杀失败 - 用户:{}, 商品:{}, 原因:{}", userId, productId, result);
                return SeckillResult.fail(result);
            }
            
        } catch (Exception e) {
            log.error("秒杀失败 - 用户:{}, 商品:{}", userId, productId, e);
            return SeckillResult.fail("系统异常，请重试");
        }
    }

    public void initProductStock(Long productId) {
        SeckillProduct product = databaseStockService.getProduct(productId);
        if (product != null) {
            redisStockService.initStock(productId, product.getAvailableStock());
            log.info("初始化商品库存到Redis - 商品:{}, 库存:{}", productId, product.getAvailableStock());
        }
    }

    public SeckillProduct getProduct(Long productId) {
        return databaseStockService.getProduct(productId);
    }

    public Integer getRedisStock(Long productId) {
        return redisStockService.getStock(productId);
    }

    private String generateOrderNo(Long userId, Long productId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        long counter = orderCounter.incrementAndGet();
        return "SK" + timestamp + userId + productId + String.format("%03d", counter % 1000);
    }

    public static class SeckillResult {
        private boolean success;
        private String message;
        private String orderNo;
        private Integer remainStock;

        private SeckillResult(boolean success, String message, String orderNo, Integer remainStock) {
            this.success = success;
            this.message = message;
            this.orderNo = orderNo;
            this.remainStock = remainStock;
        }

        public static SeckillResult success(String orderNo, Integer remainStock) {
            return new SeckillResult(true, "秒杀成功", orderNo, remainStock);
        }

        public static SeckillResult fail(String message) {
            return new SeckillResult(false, message, null, null);
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public Integer getRemainStock() {
            return remainStock;
        }
    }
}