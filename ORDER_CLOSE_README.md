# 订单关闭模块设计文档

## 概述

订单关闭模块采用生产者-消费者模式，用于自动关闭超时未支付的订单。系统通过定时任务扫描数据库，将超时订单放入阻塞队列，然后由消费者线程池处理订单关闭逻辑。

## 架构设计

### 核心组件

1. **OrderCloseProducer（生产者）**
   - 定时扫描数据库中的超时订单
   - 分页查询，避免一次性加载大量数据
   - 将订单转换为关闭任务放入阻塞队列

2. **OrderCloseConsumer（消费者）**
   - 线程池从阻塞队列中取出任务
   - 执行订单关闭逻辑：更新订单状态、恢复库存、记录日志
   - 支持失败重试机制

3. **BlockingQueue（阻塞队列）**
   - 解耦生产者和消费者
   - 提供缓冲能力，应对流量波动

4. **OrderCloseConfig（配置管理）**
   - 统一管理所有配置参数
   - 支持动态配置调整

## 关键特性

### 1. 高性能
- 分页查询，避免内存溢出
- 线程池并发处理，提高吞吐量
- 批次间隔控制，减少数据库压力

### 2. 高可靠性
- 乐观锁防止并发冲突
- 事务保证数据一致性
- 失败重试机制

### 3. 可监控
- 提供详细的统计信息
- 健康检查接口
- 完整的日志记录

### 4. 可配置
- 支持开关控制
- 灵活的参数配置
- 运行时状态监控

## 配置说明

```properties
# 订单关闭配置
order.close.enabled=true                        # 是否启用订单关闭功能
order.close.timeout-minutes=30                  # 订单超时时间（分钟）
order.close.schedule-interval-seconds=60        # 定时任务执行间隔（秒）
order.close.page-size=100                       # 分页查询大小
order.close.queue-capacity=1000                 # 阻塞队列容量
order.close.core-pool-size=5                    # 消费者线程池核心线程数
order.close.max-pool-size=10                    # 消费者线程池最大线程数
order.close.thread-pool-queue-capacity=200      # 线程池队列容量
order.close.keep-alive-seconds=60               # 线程空闲时间（秒）
order.close.max-retry-count=3                   # 最大重试次数
order.close.batch-size=50                       # 批量处理大小
order.close.consumer-wait-time-ms=1000          # 消费者等待时间（毫秒）
order.close.producer-batch-interval-ms=100      # 生产者批次间隔时间（毫秒）
```

## API接口

### 1. 获取服务状态
```
GET /api/order-close/status
```

响应示例：
```json
{
  "success": true,
  "data": {
    "producer": {
      "running": true,
      "totalProduced": 1250,
      "queueSize": 15,
      "lastScanTime": 1704067200000
    },
    "consumer": {
      "running": true,
      "totalConsumed": 1235,
      "totalSuccess": 1230,
      "totalFailed": 5,
      "successRate": "99.59%",
      "activeThreads": 3,
      "queueSize": 15
    },
    "healthy": true,
    "timestamp": 1704067200000
  }
}
```

### 2. 手动触发扫描
```
POST /api/order-close/trigger-scan
```

### 3. 健康检查
```
GET /api/order-close/health
```

## 处理流程

### 1. 生产者流程
1. 定时任务触发扫描
2. 计算超时阈值时间
3. 分页查询超时订单
4. 转换为关闭任务
5. 放入阻塞队列
6. 记录统计信息

### 2. 消费者流程
1. 从队列中取出任务
2. 检查订单当前状态
3. 更新订单状态为已取消
4. 恢复Redis库存
5. 恢复数据库库存
6. 记录库存流水
7. 处理失败重试

## 数据库变更

### 新增方法
在 `SeckillProductMapper` 中新增：
```java
@Update("UPDATE seckill_product SET available_stock = available_stock + #{quantity}, version = version + 1 " +
        "WHERE id = #{productId} AND version = #{version}")
int restoreStock(@Param("productId") Long productId, @Param("quantity") Integer quantity, @Param("version") Integer version);
```

## 监控指标

### 生产者指标
- 是否运行中
- 累计生产任务数
- 队列当前大小
- 最后扫描时间

### 消费者指标
- 是否运行中
- 累计消费任务数
- 成功处理数
- 失败处理数
- 成功率
- 活跃线程数

## 部署注意事项

1. **资源配置**
   - 根据订单量调整线程池大小
   - 合理设置队列容量
   - 监控内存使用情况

2. **数据库优化**
   - 确保订单表有合适的索引
   - 监控慢查询
   - 考虑分库分表

3. **监控告警**
   - 设置健康检查告警
   - 监控处理成功率
   - 关注队列积压情况

## 扩展建议

1. **性能优化**
   - 可考虑使用消息队列替代内存队列
   - 支持多实例部署时的任务分片
   - 增加本地缓存减少数据库查询

2. **功能增强**
   - 支持不同商品的不同超时时间
   - 增加订单关闭前的用户通知
   - 支持手动批量关闭订单

3. **运维工具**
   - 提供管理界面
   - 支持配置热更新
   - 增加更详细的监控指标
