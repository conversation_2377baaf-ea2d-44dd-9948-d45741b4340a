spring.application.name=miaoshademo
server.port=8080

# MySQL配置
spring.datasource.url=***************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# MyBatis-Plus配置
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# Redis配置
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.timeout=5000ms
spring.data.redis.lettuce.pool.max-active=10
spring.data.redis.lettuce.pool.max-idle=10
spring.data.redis.lettuce.pool.min-idle=1

# RocketMQ配置
rocketmq.name-server=localhost:9876
rocketmq.producer.group=seckill-producer-group
rocketmq.producer.send-message-timeout=30000
rocketmq.producer.retry-times-when-send-failed=3

# 启用定时任务
spring.task.scheduling.pool.size=10

# 订单关闭配置
order.close.enabled=true
order.close.timeout-minutes=30
order.close.schedule-interval-seconds=60
order.close.page-size=100
order.close.queue-capacity=1000
order.close.core-pool-size=5
order.close.max-pool-size=10
order.close.thread-pool-queue-capacity=200
order.close.keep-alive-seconds=60
order.close.max-retry-count=3
order.close.batch-size=50
order.close.consumer-wait-time-ms=1000
order.close.producer-batch-interval-ms=100
