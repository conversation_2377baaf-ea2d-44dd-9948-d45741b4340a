# 订单关闭模块使用示例

## 1. 启动应用

确保配置文件中启用了订单关闭功能：

```properties
# application.properties
order.close.enabled=true
order.close.timeout-minutes=30
```

启动应用后，订单关闭模块会自动开始工作。

## 2. 创建测试订单

首先创建一些测试订单：

```bash
# 创建秒杀订单
curl -X POST "http://localhost:8080/api/seckill/1001/1/1" \
  -H "Content-Type: application/json"
```

## 3. 模拟订单超时

为了测试订单关闭功能，可以手动修改订单的创建时间：

```sql
-- 将订单创建时间设置为35分钟前（超过30分钟超时时间）
UPDATE seckill_order 
SET created_time = DATE_SUB(NOW(), INTERVAL 35 MINUTE) 
WHERE status = 0 
LIMIT 5;
```

## 4. 监控订单关闭状态

### 查看服务状态
```bash
curl -X GET "http://localhost:8080/api/order-close/status"
```

响应示例：
```json
{
  "success": true,
  "data": {
    "producer": {
      "running": true,
      "totalProduced": 5,
      "queueSize": 0,
      "lastScanTime": 1704067200000
    },
    "consumer": {
      "running": true,
      "totalConsumed": 5,
      "totalSuccess": 5,
      "totalFailed": 0,
      "successRate": "100.00%",
      "activeThreads": 2,
      "queueSize": 0
    },
    "healthy": true,
    "timestamp": 1704067200000
  }
}
```

### 健康检查
```bash
curl -X GET "http://localhost:8080/api/order-close/health"
```

### 手动触发扫描
```bash
curl -X POST "http://localhost:8080/api/order-close/trigger-scan"
```

## 5. 验证订单关闭结果

### 检查订单状态
```sql
-- 查看订单状态变化
SELECT id, order_no, status, created_time, updated_time 
FROM seckill_order 
WHERE status = 2  -- 2表示已取消
ORDER BY updated_time DESC;
```

### 检查库存恢复
```sql
-- 查看商品库存
SELECT id, product_name, available_stock, total_stock 
FROM seckill_product;

-- 查看库存流水
SELECT * FROM stock_log 
WHERE operation_type = 2  -- 2表示取消返还
ORDER BY created_time DESC;
```

### 检查Redis库存
```bash
# 连接Redis查看库存
redis-cli
> GET seckill:stock:1
```

## 6. 日志监控

查看应用日志，关注订单关闭相关的日志：

```bash
# 查看订单关闭相关日志
tail -f logs/application.log | grep "订单关闭"
```

关键日志示例：
```
2025-01-01 12:00:00 INFO  OrderCloseProducer - 开始扫描超时订单，超时时间: 30 分钟
2025-01-01 12:00:01 INFO  OrderCloseProducer - 第 1 页查询到 5 个超时订单
2025-01-01 12:00:01 INFO  OrderCloseConsumer - 开始处理订单关闭任务: SK20250101120000123456001
2025-01-01 12:00:01 INFO  OrderCloseConsumer - Redis库存恢复成功: 商品ID=1, 数量=1
2025-01-01 12:00:01 INFO  OrderCloseConsumer - 数据库库存恢复成功 - 商品ID:1, 恢复数量:1
2025-01-01 12:00:01 INFO  OrderCloseConsumer - 订单关闭成功: SK20250101120000123456001
```

## 7. 性能调优

根据实际业务量调整配置：

```properties
# 高并发场景配置示例
order.close.page-size=200                    # 增加分页大小
order.close.queue-capacity=2000              # 增加队列容量
order.close.core-pool-size=10                # 增加核心线程数
order.close.max-pool-size=20                 # 增加最大线程数
order.close.schedule-interval-seconds=30     # 减少扫描间隔
```

## 8. 故障排查

### 常见问题

1. **订单没有被关闭**
   - 检查配置是否正确
   - 确认订单确实超时
   - 查看日志是否有异常

2. **库存没有恢复**
   - 检查数据库连接
   - 查看Redis连接状态
   - 检查库存流水记录

3. **性能问题**
   - 监控线程池使用情况
   - 检查队列积压情况
   - 优化数据库查询

### 调试命令

```sql
-- 查看待处理的超时订单
SELECT COUNT(*) FROM seckill_order 
WHERE status = 0 
AND created_time <= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
AND deleted = 0;

-- 查看最近的订单关闭记录
SELECT * FROM stock_log 
WHERE operation_type = 2 
AND created_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY created_time DESC;
```

## 9. 监控告警

建议设置以下监控告警：

1. **服务健康状态**
   - 监控 `/api/order-close/health` 接口
   - 告警条件：status != "UP"

2. **处理成功率**
   - 监控成功率低于95%
   - 检查失败原因

3. **队列积压**
   - 监控队列大小
   - 告警条件：queueSize > 500

4. **处理延迟**
   - 监控订单从超时到关闭的时间
   - 告警条件：延迟超过10分钟

## 10. 扩展使用

### 自定义超时时间
可以为不同类型的商品设置不同的超时时间：

```java
// 在OrderCloseProducer中扩展
private int getTimeoutMinutes(Long productId) {
    // 根据商品类型返回不同的超时时间
    // 例如：贵重商品30分钟，普通商品15分钟
    return productService.getTimeoutMinutes(productId);
}
```

### 批量操作优化
对于大量订单的场景，可以考虑批量更新：

```java
// 批量更新订单状态
public void batchCloseOrders(List<OrderCloseTask> tasks) {
    // 实现批量更新逻辑
}
```
