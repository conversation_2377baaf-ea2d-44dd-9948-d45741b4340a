package com.exp.miaoshademo.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.exp.miaoshademo.config.OrderCloseConfig;
import com.exp.miaoshademo.dto.OrderCloseTask;
import com.exp.miaoshademo.entity.SeckillOrder;
import com.exp.miaoshademo.entity.StockLog;
import com.exp.miaoshademo.enums.OrderStatus;
import com.exp.miaoshademo.mapper.SeckillOrderMapper;
import com.exp.miaoshademo.mapper.StockLogMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单关闭消费者服务
 * 负责从阻塞队列中取出订单数据进行关闭处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderCloseConsumer {
    
    private final SeckillOrderMapper orderMapper;
    private final StockLogMapper stockLogMapper;
    private final RedisStockService redisStockService;
    private final DatabaseStockService databaseStockService;
    private final OrderCloseConfig config;
    private final BlockingQueue<OrderCloseTask> orderCloseQueue;
    
    private ThreadPoolExecutor consumerThreadPool;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong totalConsumed = new AtomicLong(0);
    private final AtomicLong totalSuccess = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);
    
    @PostConstruct
    public void init() {
        if (!config.isEnabled()) {
            log.info("订单关闭功能已禁用");
            return;
        }
        
        // 初始化线程池
        consumerThreadPool = new ThreadPoolExecutor(
                config.getCorePoolSize(),
                config.getMaxPoolSize(),
                config.getKeepAliveSeconds(),
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(config.getThreadPoolQueueCapacity()),
                new ThreadFactory() {
                    private int counter = 0;
                    @Override
                    public Thread newThread(Runnable r) {
                        return new Thread(r, "order-close-consumer-" + (++counter));
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        startConsumers();
        log.info("订单关闭消费者服务已启动，线程池大小: {}-{}", 
                config.getCorePoolSize(), config.getMaxPoolSize());
    }
    
    @PreDestroy
    public void destroy() {
        if (consumerThreadPool != null) {
            isRunning.set(false);
            consumerThreadPool.shutdown();
            try {
                if (!consumerThreadPool.awaitTermination(30, TimeUnit.SECONDS)) {
                    consumerThreadPool.shutdownNow();
                }
            } catch (InterruptedException e) {
                consumerThreadPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("订单关闭消费者服务已停止");
        }
    }
    
    /**
     * 启动消费者
     */
    private void startConsumers() {
        isRunning.set(true);
        
        for (int i = 0; i < config.getCorePoolSize(); i++) {
            consumerThreadPool.submit(this::consumeOrderCloseTasks);
        }
    }
    
    /**
     * 消费订单关闭任务
     */
    private void consumeOrderCloseTasks() {
        while (isRunning.get()) {
            try {
                OrderCloseTask task = orderCloseQueue.poll(config.getConsumerWaitTimeMs(), TimeUnit.MILLISECONDS);
                if (task != null) {
                    processOrderCloseTask(task);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("消费订单关闭任务时发生异常", e);
            }
        }
    }
    
    /**
     * 处理订单关闭任务
     */
    private void processOrderCloseTask(OrderCloseTask task) {
        totalConsumed.incrementAndGet();
        
        try {
            log.info("开始处理订单关闭任务: {}", task.getOrderNo());
            
            // 执行订单关闭逻辑
            boolean success = closeOrder(task);
            
            if (success) {
                totalSuccess.incrementAndGet();
                log.info("订单关闭成功: {}", task.getOrderNo());
            } else {
                handleFailedTask(task);
            }
            
        } catch (Exception e) {
            log.error("处理订单关闭任务失败: {}", task.getOrderNo(), e);
            handleFailedTask(task);
        }
    }
    
    /**
     * 关闭订单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean closeOrder(OrderCloseTask task) {
        try {
            // 1. 检查订单当前状态
            SeckillOrder currentOrder = orderMapper.selectById(task.getOrderId());
            if (currentOrder == null) {
                log.warn("订单不存在: {}", task.getOrderNo());
                return false;
            }
            
            if (!OrderStatus.canClose(currentOrder.getStatus())) {
                log.info("订单状态不允许关闭: {}, 当前状态: {}", 
                        task.getOrderNo(), currentOrder.getStatus());
                return true; // 认为成功，因为订单已经不是待支付状态
            }
            
            // 2. 更新订单状态为已取消
            LambdaUpdateWrapper<SeckillOrder> updateWrapper = new LambdaUpdateWrapper<SeckillOrder>()
                    .eq(SeckillOrder::getId, task.getOrderId())
                    .eq(SeckillOrder::getStatus, OrderStatus.PENDING_PAYMENT.getCode())
                    .set(SeckillOrder::getStatus, OrderStatus.CANCELLED.getCode())
                    .set(SeckillOrder::getUpdatedTime, LocalDateTime.now());
            
            int updatedRows = orderMapper.update(null, updateWrapper);
            if (updatedRows == 0) {
                log.warn("订单状态更新失败，可能已被其他线程处理: {}", task.getOrderNo());
                return false;
            }
            
            // 3. 恢复Redis库存
            try {
                redisStockService.restoreStock(task.getProductId(), task.getQuantity());
                log.info("Redis库存恢复成功: 商品ID={}, 数量={}", task.getProductId(), task.getQuantity());
            } catch (Exception e) {
                log.error("Redis库存恢复失败: 商品ID={}, 数量={}", task.getProductId(), task.getQuantity(), e);
                // Redis恢复失败不影响订单关闭，后续可通过补偿机制处理
            }
            
            // 4. 恢复数据库库存
            boolean stockRestored = databaseStockService.restoreStock(task.getProductId(), task.getQuantity());
            if (!stockRestored) {
                log.error("数据库库存恢复失败: 商品ID={}, 数量={}", task.getProductId(), task.getQuantity());
                throw new RuntimeException("数据库库存恢复失败");
            }
            
            // 5. 记录库存流水
            recordStockLog(task);
            
            log.info("订单关闭处理完成: {}", task.getOrderNo());
            return true;
            
        } catch (Exception e) {
            log.error("关闭订单时发生异常: {}", task.getOrderNo(), e);
            throw e;
        }
    }
    
    /**
     * 记录库存流水
     */
    private void recordStockLog(OrderCloseTask task) {
        try {
            StockLog stockLog = new StockLog();
            stockLog.setProductId(task.getProductId());
            stockLog.setUserId(task.getUserId());
            stockLog.setOrderNo(task.getOrderNo());
            stockLog.setChangeAmount(task.getQuantity()); // 正数表示恢复
            stockLog.setOperationType(2); // 2-取消返还
            stockLog.setStatus(1); // 1-成功
            stockLog.setCreatedTime(LocalDateTime.now());
            stockLog.setUpdatedTime(LocalDateTime.now());
            
            stockLogMapper.insert(stockLog);
            log.debug("库存流水记录成功: {}", task.getOrderNo());
            
        } catch (Exception e) {
            log.error("记录库存流水失败: {}", task.getOrderNo(), e);
            // 流水记录失败不影响主流程
        }
    }
    
    /**
     * 处理失败的任务
     */
    private void handleFailedTask(OrderCloseTask task) {
        totalFailed.incrementAndGet();
        task.incrementRetryCount();
        
        if (!task.isMaxRetryExceeded(config.getMaxRetryCount())) {
            // 重新放入队列重试
            if (orderCloseQueue.offer(task)) {
                log.info("订单关闭任务重新放入队列重试: {}, 重试次数: {}", 
                        task.getOrderNo(), task.getRetryCount());
            } else {
                log.error("队列已满，无法重试订单关闭任务: {}", task.getOrderNo());
            }
        } else {
            log.error("订单关闭任务超过最大重试次数，放弃处理: {}, 重试次数: {}", 
                    task.getOrderNo(), task.getRetryCount());
            // 可以考虑将失败的任务记录到数据库或发送告警
        }
    }
    
    /**
     * 获取消费者统计信息
     */
    public ConsumerStats getStats() {
        return new ConsumerStats(
                isRunning.get(),
                totalConsumed.get(),
                totalSuccess.get(),
                totalFailed.get(),
                consumerThreadPool != null ? consumerThreadPool.getActiveCount() : 0,
                orderCloseQueue.size()
        );
    }
    
    /**
     * 消费者统计信息
     */
    public static class ConsumerStats {
        private final boolean running;
        private final long totalConsumed;
        private final long totalSuccess;
        private final long totalFailed;
        private final int activeThreads;
        private final int queueSize;
        
        public ConsumerStats(boolean running, long totalConsumed, long totalSuccess, 
                           long totalFailed, int activeThreads, int queueSize) {
            this.running = running;
            this.totalConsumed = totalConsumed;
            this.totalSuccess = totalSuccess;
            this.totalFailed = totalFailed;
            this.activeThreads = activeThreads;
            this.queueSize = queueSize;
        }
        
        // Getters
        public boolean isRunning() { return running; }
        public long getTotalConsumed() { return totalConsumed; }
        public long getTotalSuccess() { return totalSuccess; }
        public long getTotalFailed() { return totalFailed; }
        public int getActiveThreads() { return activeThreads; }
        public int getQueueSize() { return queueSize; }
        public double getSuccessRate() { 
            return totalConsumed > 0 ? (double) totalSuccess / totalConsumed : 0.0; 
        }
    }
}
